# 野火K210激光瞄准系统硬件连接指南

## 🔌 单线控制双舵机连接方案

### 硬件架构说明

您使用的是**串联式双舵机云台**，这是一种简化的连接方案：

```
┌─────────────────┐
│   野火K210      │
│                 │
│ 引脚0(RX) ──────┼─────────┐
│ GND ────────────┼─────┐   │
│ 5V ─────────────┼───┐ │   │
└─────────────────┘   │ │   │
                      │ │   │
                ┌─────▼─▼───▼─────┐
                │   上方舵机       │
                │   (X轴/水平)     │
                │   ID: 0x01      │
                └─────────┬───────┘
                          │ 三根连接线
                          │ (信号+电源+地)
                ┌─────────▼───────┐
                │   下方舵机       │
                │   (Y轴/垂直)     │
                │   ID: 0x02      │
                └─────────────────┘
```

### 连接详情

#### 主要连接
- **K210引脚0(RX)** → **上方舵机S信号线**
- **K210 GND** → **舵机电源地线**
- **K210 5V** → **舵机电源正极**

#### 舵机间连接
- 上方舵机和下方舵机之间有**三根线连接**：
  1. **信号线**：传递控制指令
  2. **电源正极**：共享电源
  3. **电源地线**：共享地线

### 工作原理

1. **K210发送指令** → 通过引脚0发送到上方舵机
2. **上方舵机接收** → 判断指令是否为自己的ID(0x01)
3. **指令转发** → 将指令通过连接线传递给下方舵机
4. **下方舵机接收** → 判断指令是否为自己的ID(0x02)
5. **执行动作** → 对应舵机执行相应动作

## 🔧 硬件安装步骤

### 步骤1：舵机云台组装
```bash
1. 将下方舵机固定在底座上
2. 将上方舵机安装在下方舵机的输出轴上
3. 确保两个舵机之间的三根连接线牢固
4. 检查舵机转动范围，确保无卡顿
```

### 步骤2：K210相机安装
```bash
1. 将野火K210相机固定在上方舵机的输出轴上
2. 确保相机镜头方向与舵机转动方向一致
3. 固定要牢固，避免震动影响图像质量
```

### 步骤3：激光笔安装
```bash
1. 将激光笔固定在K210相机旁边
2. 激光笔方向应与相机视野方向基本一致
3. 可以使用小型支架或胶带固定
4. 确保激光笔不会遮挡相机视野
```

### 步骤4：电源连接
```bash
1. 连接K210的GND到舵机电源地线
2. 连接K210的5V到舵机电源正极
3. 确保电源供应充足（建议5V/2A以上）
4. 检查所有连接是否牢固
```

### 步骤5：信号线连接
```bash
1. 将K210的引脚0(RX)连接到上方舵机的S信号线
2. 确保连接牢固，避免接触不良
3. 可以使用杜邦线或直接焊接
```

## ⚙️ 软件配置说明

### 配置文件修改

代码已自动适配您的硬件连接方式：

```python
# config.py中的关键配置
UART_SETTINGS = {
    'uart_num': 1,            # 使用UART1
    'baudrate': 115200,       # 波特率
    'rx_pin': 0,              # RX引脚连接到上方舵机S引脚
    'tx_pin': None,           # 不使用TX引脚
    'single_wire_mode': True  # 单线模式标志
}

SERVO_SETTINGS = {
    'ID1': 0x01,              # X轴舵机ID（上方舵机）
    'ID2': 0x02,              # Y轴舵机ID（下方舵机）
    'interval': 1000,         # 控制间隔(ms)
}
```

### 控制逻辑

系统使用以下逻辑控制双舵机：

1. **发送X轴指令**：向ID1舵机发送位置指令
2. **延时等待**：等待20ms确保指令传输完成
3. **发送Y轴指令**：向ID2舵机发送位置指令
4. **延时等待**：等待10ms确保指令执行

## 🧪 测试验证

### 连接测试

运行以下测试确认连接正确：

```python
# 在CanMV IDE中运行
from hardware_manager import HardwareManager

hardware = HardwareManager()
hardware.init_uart()

# 测试舵机响应
print("测试中心位置...")
hardware.set_servo_center_position()

print("测试左转...")
hardware.set_servo_left_position()

print("测试右转...")
hardware.set_servo_right_position()

print("回到中心...")
hardware.set_servo_center_position()
```

### 预期结果

- ✅ 上方舵机应该响应水平转动指令
- ✅ 下方舵机应该响应垂直转动指令
- ✅ 两个舵机动作应该平滑无卡顿
- ✅ 激光笔应该跟随舵机移动

## 🔍 故障排除

### 常见问题

#### 问题1：舵机不响应
**可能原因**：
- 信号线连接不良
- 电源供应不足
- 舵机ID配置错误

**解决方案**：
```bash
1. 检查引脚0到上方舵机S线的连接
2. 确认电源电压和电流充足
3. 验证舵机ID设置（ID1=0x01, ID2=0x02）
```

#### 问题2：只有一个舵机动作
**可能原因**：
- 舵机间连接线松动
- 指令发送间隔太短
- 舵机损坏

**解决方案**：
```bash
1. 检查舵机间的三根连接线
2. 增加指令发送间隔时间
3. 单独测试每个舵机
```

#### 问题3：舵机抖动或异常
**可能原因**：
- 电源纹波过大
- 信号干扰
- 机械卡顿

**解决方案**：
```bash
1. 使用稳定的电源供应
2. 检查信号线屏蔽
3. 检查机械安装是否顺畅
```

## 📊 性能参数

### 推荐规格

- **舵机类型**：数字舵机，支持串口控制
- **工作电压**：5V ± 0.5V
- **工作电流**：每个舵机 < 1A
- **响应时间**：< 100ms
- **精度**：± 1度
- **转动范围**：180度或360度

### 系统性能

- **控制精度**：± 1像素
- **响应时间**：< 100ms
- **稳定性**：连续工作 > 24小时
- **环境温度**：-10°C ~ +60°C

---

## 📞 技术支持

如果在硬件连接过程中遇到问题，请提供：

1. **连接照片**：实际的硬件连接图片
2. **舵机型号**：具体的舵机型号和规格
3. **错误现象**：详细描述问题现象
4. **测试结果**：运行测试代码的输出结果

**技术支持**：米醋电子工作室
**邮箱**：<EMAIL>

---

**🎯 硬件连接完成后，您就可以开始使用激光瞄准系统了！**
