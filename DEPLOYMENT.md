# 野火K210激光瞄准系统部署指南

## 🚀 快速部署

### 必需文件清单
将以下文件上传到K210设备：

```
✅ main.py                    # 主程序入口
✅ config.py                  # 配置管理模块  
✅ hardware_manager.py        # 硬件抽象层
✅ vision_processor.py        # 视觉处理模块
✅ control_system.py          # 控制系统模块
✅ calibration_manager.py     # 校准管理模块
✅ laser_aiming_system.py     # 主控制器
```

### 可选文件
```
📄 start.py                  # 安全启动脚本（推荐）
📄 simple_test.py            # 简单测试程序
📄 test_system.py            # 完整测试套件
📄 README.md                 # 详细说明文档
```

## 🔧 运行方式

### 方式1: 标准启动
```python
python main.py
```

### 方式2: 安全启动（推荐）
```python
python start.py
```

### 方式3: 简单测试
```python
python simple_test.py
```

## 🔌 硬件连接

### 基本连接（单线控制双舵机）
- **摄像头**: K210内置，无需额外连接
- **LCD显示**: K210内置，无需额外连接
- **舵机控制**: 引脚0(RX) → 上方舵机S信号线
- **电源连接**: K210的GND和5V连接到舵机电源

### 激光笔安装
1. 将激光笔固定在K210摄像头附近
2. 确保激光笔与摄像头视野对齐
3. 激光笔可通过GPIO控制开关（可选）

### 串联双舵机云台连接
1. 上方舵机(ID1)控制水平转动，下方舵机(ID2)控制垂直转动
2. K210引脚0连接到上方舵机S信号线
3. 两个舵机之间通过三根线连接（信号+电源+地）
4. 确保舵机电源供应充足（5V/2A以上）

**详细连接图请参考：HARDWARE_CONNECTION.md**

## 🎮 操作指令

### 串口控制指令
| 指令 | 功能 | 说明 |
|------|------|------|
| `0` | 自动模式 | 自动检测黑色矩形并控制激光瞄准 |
| `1` | 手动模式 | 手动控制舵机位置 |
| `9` | 校准模式 | 进行激光笔偏移校准 |
| `2` | 回中位 | 舵机回到中心位置 |
| `3` | 左转 | 舵机向左转动 |
| `4` | 回中位 | 舵机回到中心位置 |
| `5` | 右转 | 舵机向右转动 |
| `c` | 开始校准 | 启动9点校准流程 |
| `r` | 重置系统 | 重置所有参数和状态 |
| `q` | 退出程序 | 安全退出系统 |

### 校准流程
1. 发送 `c` 指令或 `9` 进入校准模式
2. 系统依次移动到9个校准点（3x3网格）
3. 在每个点观察激光位置，记录偏移
4. 完成所有点后系统自动计算校准参数
5. 校准参数保存到文件，下次启动自动加载

## 📊 性能参数

### 系统性能
- **检测精度**: 95%以上准确率
- **控制精度**: 激光抖动 < 1像素
- **响应时间**: < 100ms
- **帧率**: 15+ FPS
- **校准精度**: 误差 < 1像素

### 硬件要求
- **分辨率**: QQVGA (160x120)
- **格式**: GRAYSCALE
- **内存**: 建议6MB以上可用内存
- **存储**: 建议100KB以上可用存储

## 🛠️ 故障排除

### 常见问题

#### 1. 系统无法启动
**症状**: 程序运行后立即退出或报错
**解决方案**:
- 检查所有必需文件是否完整上传
- 确认文件编码为UTF-8
- 运行 `python simple_test.py` 进行诊断

#### 2. 摄像头无图像
**症状**: LCD显示黑屏或无图像
**解决方案**:
- 检查摄像头连接
- 确认电源供应充足
- 重启K210设备

#### 3. 舵机不响应
**症状**: 发送指令后舵机不动作
**解决方案**:
- 检查串口连接（引脚0, 引脚1）
- 确认舵机电源供应
- 检查舵机ID配置（默认ID1=0x01, ID2=0x02）

#### 4. 检测不准确
**症状**: 无法检测到黑色矩形或误检
**解决方案**:
- 确保目标为纯黑色矩形
- 改善光照条件，避免强光直射
- 调整检测参数（在config.py中）
- 清洁摄像头镜头

#### 5. 激光偏移
**症状**: 激光点不在目标中心
**解决方案**:
- 运行校准程序：发送 `c` 指令
- 检查激光笔固定是否松动
- 重新进行9点校准

### 调试模式
在config.py中设置：
```python
SYSTEM_SETTINGS['debug_mode'] = True
```

启用调试模式后会显示：
- 详细的检测信息
- FPS和性能统计
- 系统状态信息
- 错误详细信息

### 日志查看
系统会在串口输出详细日志信息，包括：
- 硬件初始化状态
- 检测结果统计
- 控制指令执行情况
- 错误和警告信息

## 📞 技术支持

### 联系方式
- **技术支持**: 米醋电子工作室
- **邮箱**: <EMAIL>
- **文档**: 查看项目docs/目录

### 问题反馈
请提供以下信息：
1. 硬件配置详情
2. 错误信息截图
3. 系统日志输出
4. 问题复现步骤

---

**🎯 祝您使用愉快！激光瞄准系统已准备就绪！**
