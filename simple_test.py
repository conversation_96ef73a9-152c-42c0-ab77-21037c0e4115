# -*- coding: utf-8 -*-
# 野火K210激光瞄准系统简单测试
# 版权所有: 米醋电子工作室

"""
简单测试程序

这是一个最简化的测试程序，用于验证基本功能。
如果完整系统有问题，可以先运行这个简单版本。
"""

def test_basic_imports():
    """测试基本导入"""
    print("=== 测试基本导入 ===")
    
    try:
        # 测试配置模块
        from config import Config
        print("✓ Config模块导入成功")
        
        # 测试配置获取
        camera_config = Config.get_camera_settings()
        print(f"✓ 摄像头配置: {camera_config['pixformat']}")
        
        return True
    except Exception as e:
        print(f"✗ 基本导入失败: {e}")
        return False

def test_hardware_basic():
    """测试硬件基础功能"""
    print("\n=== 测试硬件基础功能 ===")
    
    try:
        from hardware_manager import HardwareManager
        print("✓ HardwareManager模块导入成功")
        
        # 创建硬件管理器
        hardware = HardwareManager()
        print("✓ 硬件管理器创建成功")
        
        # 获取硬件状态
        status = hardware.get_hardware_status()
        print(f"✓ 硬件状态: {status}")
        
        return True
    except Exception as e:
        print(f"✗ 硬件测试失败: {e}")
        return False

def test_vision_basic():
    """测试视觉处理基础功能"""
    print("\n=== 测试视觉处理基础功能 ===")
    
    try:
        from vision_processor import VisionProcessor
        print("✓ VisionProcessor模块导入成功")
        
        # 创建视觉处理器
        vision = VisionProcessor()
        print("✓ 视觉处理器创建成功")
        
        # 获取检测统计
        stats = vision.get_detection_stats()
        print(f"✓ 检测统计: {stats}")
        
        return True
    except Exception as e:
        print(f"✗ 视觉处理测试失败: {e}")
        return False

def test_control_basic():
    """测试控制系统基础功能"""
    print("\n=== 测试控制系统基础功能 ===")
    
    try:
        from control_system import PIDController
        print("✓ PIDController模块导入成功")
        
        # 创建PID控制器
        pid = PIDController(1.0, 0.1, 0.05, setpoint=80)
        print("✓ PID控制器创建成功")
        
        # 测试PID计算
        output = pid.update(75)
        print(f"✓ PID输出: {output:.2f}")
        
        return True
    except Exception as e:
        print(f"✗ 控制系统测试失败: {e}")
        return False

def test_main_system():
    """测试主系统"""
    print("\n=== 测试主系统 ===")
    
    try:
        from laser_aiming_system import LaserAimingSystem
        print("✓ LaserAimingSystem模块导入成功")
        
        # 创建激光瞄准系统
        laser_system = LaserAimingSystem()
        print("✓ 激光瞄准系统创建成功")
        
        # 获取系统状态
        status = laser_system.get_system_status()
        print(f"✓ 系统状态: 运行={status['running']}")
        
        return True
    except Exception as e:
        print(f"✗ 主系统测试失败: {e}")
        return False

def run_simple_test():
    """运行简单测试"""
    print("野火K210激光瞄准系统 - 简单测试")
    print("版权所有: 米醋电子工作室")
    print("=" * 40)
    
    tests = [
        ("基本导入", test_basic_imports),
        ("硬件基础", test_hardware_basic),
        ("视觉处理", test_vision_basic),
        ("控制系统", test_control_basic),
        ("主系统", test_main_system)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 40)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关模块。")
        return False

def main():
    """主函数"""
    try:
        success = run_simple_test()
        
        if success:
            print("\n是否启动完整系统？(y/n)")
            # 在K210环境下，可能没有input函数，所以直接启动
            print("自动启动完整系统...")
            
            from laser_aiming_system import LaserAimingSystem
            laser_system = LaserAimingSystem()
            laser_system.run()
        else:
            print("测试失败，请检查代码和硬件连接")
            
    except KeyboardInterrupt:
        print("用户中断")
    except Exception as e:
        print(f"程序异常: {e}")

if __name__ == "__main__":
    main()
