# 部署检查脚本
# 确保所有文件都准备好在K210上部署

import os

def check_files():
    """检查必需文件是否存在"""
    required_files = [
        'main.py',           # 主程序入口
        'config.py',         # 配置管理
        'hardware_manager.py', # 硬件抽象层
        'vision_processor.py', # 视觉处理
        'control_system.py',   # 控制系统
        'calibration_manager.py', # 校准管理
        'laser_aiming_system.py', # 主控制器
        'start.py',          # 启动脚本
        'README.md'          # 说明文档
    ]
    
    optional_files = [
        'test_system.py',    # 测试程序
        'syntax_check.py',   # 语法检查
        'deploy_check.py'    # 部署检查
    ]
    
    print("=== 必需文件检查 ===")
    missing_required = []
    
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✓ {file} ({size} bytes)")
        else:
            print(f"✗ {file} - 缺失")
            missing_required.append(file)
    
    print("\n=== 可选文件检查 ===")
    for file in optional_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✓ {file} ({size} bytes)")
        else:
            print(f"- {file} - 不存在")
    
    return len(missing_required) == 0

def check_docs():
    """检查文档目录"""
    print("\n=== 文档目录检查 ===")
    
    if os.path.exists('docs'):
        print("✓ docs/ 目录存在")
        
        if os.path.exists('docs/architecture'):
            print("✓ docs/architecture/ 目录存在")
            
            arch_file = 'docs/architecture/Architecture_LaserAiming_v1.0.md'
            if os.path.exists(arch_file):
                print(f"✓ {arch_file} 存在")
            else:
                print(f"✗ {arch_file} 缺失")
        else:
            print("✗ docs/architecture/ 目录缺失")
    else:
        print("✗ docs/ 目录缺失")

def print_deployment_guide():
    """打印部署指南"""
    print("\n" + "="*50)
    print("野火K210激光瞄准系统部署指南")
    print("="*50)
    print()
    print("1. 文件上传:")
    print("   - 将以下核心文件上传到K210设备:")
    print("     * main.py")
    print("     * config.py")
    print("     * hardware_manager.py")
    print("     * vision_processor.py")
    print("     * control_system.py")
    print("     * calibration_manager.py")
    print("     * laser_aiming_system.py")
    print("     * start.py (推荐)")
    print()
    print("2. 运行方式:")
    print("   方式1: python main.py")
    print("   方式2: python start.py (推荐，包含错误处理)")
    print()
    print("3. 硬件连接:")
    print("   - 摄像头: 已集成在K210上")
    print("   - 串口: 引脚0(TX), 引脚1(RX)")
    print("   - 舵机: 通过串口控制")
    print("   - 激光笔: 固定在摄像头上")
    print()
    print("4. 串口指令:")
    print("   - '0': 自动模式")
    print("   - '1': 手动模式")
    print("   - '9': 校准模式")
    print("   - '2','4': 回中位")
    print("   - '3': 左转")
    print("   - '5': 右转")
    print("   - 'c': 开始校准")
    print("   - 'r': 重置系统")
    print("   - 'q': 退出程序")
    print()
    print("5. 故障排除:")
    print("   - 检查硬件连接")
    print("   - 确认串口配置")
    print("   - 查看调试输出")
    print("   - 运行 test_system.py 进行诊断")
    print()
    print("技术支持: 米醋电子工作室")
    print("="*50)

def main():
    """主函数"""
    print("野火K210激光瞄准系统部署检查")
    print("版权所有: 米醋电子工作室")
    print()
    
    # 检查文件
    files_ok = check_files()
    
    # 检查文档
    check_docs()
    
    # 总结
    print("\n=== 检查结果 ===")
    if files_ok:
        print("🎉 所有必需文件检查通过！")
        print("系统已准备好部署到K210设备")
    else:
        print("⚠️ 发现缺失文件，请补充后重试")
    
    # 打印部署指南
    print_deployment_guide()

if __name__ == "__main__":
    main()
