# K210模拟环境 - 用于PC端测试
# 版权所有: 米醋电子工作室

"""
K210模拟环境

这个模块提供了K210硬件模块的模拟实现，
用于在PC环境下测试代码逻辑。
"""

import time
import random

# 模拟machine模块
class MockUART:
    def __init__(self, uart_id, baudrate, read_buf_len=4096):
        self.uart_id = uart_id
        self.baudrate = baudrate
        self.read_buf_len = read_buf_len
        print(f"Mock UART{uart_id} initialized at {baudrate} baud")
    
    def write(self, data):
        print(f"Mock UART write: {data}")
        return len(data)
    
    def read(self, size):
        # 模拟随机串口数据
        if random.random() < 0.1:  # 10%概率有数据
            return str(random.choice(['0', '1', '2', '3', '4', '5'])).encode()
        return None
    
    def deinit(self):
        print("Mock UART deinitialized")

class MockMachine:
    UART1 = 1
    UART2 = 2
    
    @staticmethod
    def UART(uart_id, baudrate, read_buf_len=4096):
        return MockUART(uart_id, baudrate, read_buf_len)

# 模拟sensor模块
class MockSensor:
    GRAYSCALE = 0
    RGB565 = 1
    QQVGA = 0
    QVGA = 1
    
    @staticmethod
    def reset():
        print("Mock sensor reset")
    
    @staticmethod
    def set_pixformat(format):
        print(f"Mock sensor set pixformat: {format}")
    
    @staticmethod
    def set_framesize(size):
        print(f"Mock sensor set framesize: {size}")
    
    @staticmethod
    def set_brightness(value):
        print(f"Mock sensor set brightness: {value}")
    
    @staticmethod
    def set_saturation(value):
        print(f"Mock sensor set saturation: {value}")
    
    @staticmethod
    def skip_frames(time=1000):
        print(f"Mock sensor skip frames: {time}ms")
    
    @staticmethod
    def snapshot():
        return MockImage()

# 模拟image模块
class MockImage:
    def __init__(self, width=160, height=120):
        self.width = width
        self.height = height
    
    def laplacian(self, size, sharpen=False):
        print(f"Mock image laplacian: size={size}, sharpen={sharpen}")
        return self
    
    def binary(self, thresholds):
        print(f"Mock image binary: {thresholds}")
        return self
    
    def erode(self, size):
        print(f"Mock image erode: {size}")
        return self
    
    def dilate(self, size):
        print(f"Mock image dilate: {size}")
        return self
    
    def find_rects(self, threshold=10000):
        # 模拟找到1-2个矩形
        rects = []
        for i in range(random.randint(0, 2)):
            rects.append(MockRect())
        return rects
    
    def find_blobs(self, thresholds, area_threshold=100, pixels_threshold=100):
        # 模拟找到0-3个blob
        blobs = []
        for i in range(random.randint(0, 3)):
            blobs.append(MockBlob())
        return blobs
    
    def draw_rectangle(self, x, y, w, h, color=(255, 0, 0), thickness=1):
        print(f"Mock draw rectangle: ({x},{y},{w},{h}) color={color}")
    
    def draw_circle(self, x, y, radius, color=(255, 0, 0), fill=False, thickness=1):
        print(f"Mock draw circle: ({x},{y}) r={radius} color={color}")
    
    def draw_string(self, x, y, text, color=(255, 255, 255), scale=1):
        print(f"Mock draw string: ({x},{y}) '{text}' color={color}")
    
    def draw_cross(self, x, y, color=(255, 0, 0), size=5):
        print(f"Mock draw cross: ({x},{y}) color={color} size={size}")
    
    def copy(self):
        return MockImage(self.width, self.height)

class MockRect:
    def __init__(self):
        self.x_val = random.randint(20, 100)
        self.y_val = random.randint(20, 80)
        self.w_val = random.randint(20, 60)
        self.h_val = random.randint(20, 40)
    
    def x(self): return self.x_val
    def y(self): return self.y_val
    def w(self): return self.w_val
    def h(self): return self.h_val
    def rect(self): return (self.x_val, self.y_val, self.w_val, self.h_val)
    def corners(self):
        return [
            (self.x_val, self.y_val),
            (self.x_val + self.w_val, self.y_val),
            (self.x_val + self.w_val, self.y_val + self.h_val),
            (self.x_val, self.y_val + self.h_val)
        ]

class MockBlob:
    def __init__(self):
        self.x_val = random.randint(20, 100)
        self.y_val = random.randint(20, 80)
        self.w_val = random.randint(20, 60)
        self.h_val = random.randint(20, 40)
        self.area_val = self.w_val * self.h_val
    
    def x(self): return self.x_val
    def y(self): return self.y_val
    def w(self): return self.w_val
    def h(self): return self.h_val
    def area(self): return self.area_val

# 模拟lcd模块
class MockLCD:
    WHITE = (255, 255, 255)
    
    @staticmethod
    def init():
        print("Mock LCD initialized")
    
    @staticmethod
    def clear(color=None):
        print(f"Mock LCD clear: {color}")
    
    @staticmethod
    def display(img):
        print("Mock LCD display image")

# 模拟fpioa_manager
class MockFPIOA:
    UART1_RX = 1
    UART1_TX = 2

class MockFPIOAManager:
    fpioa = MockFPIOA()
    
    @staticmethod
    def register(pin, function, force=False):
        print(f"Mock FPIOA register: pin={pin}, function={function}, force={force}")

# 模拟gc模块
class MockGC:
    @staticmethod
    def collect():
        print("Mock garbage collection")

# 模拟time模块增强
class MockTime:
    @staticmethod
    def ticks_ms():
        return int(time.time() * 1000)
    
    @staticmethod
    def ticks_diff(new, old):
        return new - old
    
    @staticmethod
    def sleep(seconds):
        time.sleep(seconds)
    
    @staticmethod
    def clock():
        return MockClock()

class MockClock:
    def __init__(self):
        self.last_time = time.time()
        self.frame_count = 0
    
    def tick(self):
        self.frame_count += 1
        current_time = time.time()
        self.last_time = current_time
    
    def fps(self):
        return 15.0 + random.uniform(-2, 2)  # 模拟13-17 FPS

def setup_mock_environment():
    """设置模拟环境"""
    import sys
    
    # 创建模拟模块
    sys.modules['machine'] = type('MockMachine', (), {
        'UART': MockMachine.UART,
        'Timer': lambda: None,
        'PWM': lambda: None
    })()
    
    sys.modules['sensor'] = MockSensor()
    sys.modules['image'] = type('MockImage', (), {'Image': MockImage})()
    sys.modules['lcd'] = MockLCD()
    sys.modules['fpioa_manager'] = type('MockFPIOAManager', (), {
        'fm': MockFPIOAManager()
    })()
    sys.modules['gc'] = MockGC()
    
    # 增强time模块
    import time
    time.ticks_ms = MockTime.ticks_ms
    time.ticks_diff = MockTime.ticks_diff
    time.clock = MockTime.clock
    
    print("Mock K210 environment setup complete")

if __name__ == "__main__":
    setup_mock_environment()
    print("Mock environment ready for testing")
