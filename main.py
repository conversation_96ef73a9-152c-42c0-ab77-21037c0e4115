# 野火K210激光瞄准系统 - 模块化重构版本
# 版权所有: 米醋电子工作室
# 基于原有01Studio代码进行模块化改造，实现激光笔精准瞄准功能

"""
野火K210激光瞄准系统主程序

功能特性:
- 黑色矩形目标检测与跟踪
- 激光笔精准瞄准控制
- 双轴舵机PID控制
- 激光偏移校准系统
- 模块化低耦合架构
- 实时图像处理与显示

使用说明:
1. 将K210相机固定在舵机云台上
2. 在相机上绑定激光笔
3. 运行程序进行目标跟踪
4. 使用串口指令进行控制:
   - '0': 自动模式
   - '1': 手动模式
   - '9': 校准模式
   - '2','4': 回中位
   - '3': 左转
   - '5': 右转
   - 'c': 开始校准
   - 'r': 重置系统
   - 'q': 退出程序

硬件要求:
- 野火K210 AI视觉相机
- 双轴舵机云台
- 激光笔模块
- 串口通信连接
"""

# 导入新的模块化组件
from laser_aiming_system import LaserAimingSystem

# 保持向后兼容的硬件初始化函数 (已废弃，仅供参考)
def hardware_init():
    """
    原有硬件初始化函数 - 已废弃
    新版本使用HardwareManager类进行硬件管理
    """
    print("警告: hardware_init()函数已废弃，请使用HardwareManager类")
    print("参考: hardware_manager.py")

# 保持向后兼容的舵机控制函数 (已废弃，仅供参考)
def func_servo(id0, posit0, interval0):
    """
    原有舵机控制函数 - 已废弃
    新版本使用HardwareManager.send_servo_command()方法
    """
    print("警告: func_servo()函数已废弃，请使用HardwareManager.send_servo_command()方法")
    print("参考: hardware_manager.py")

# 新版本主程序入口
def main():
    """
    新版本主程序入口 - 使用模块化架构

    这个函数现在使用LaserAimingSystem类来管理整个系统，
    替代了原有的单体架构实现。
    """
    print("=== 野火K210激光瞄准系统 v2.0 ===")
    print("版权所有: 米醋电子工作室")
    print("基于模块化架构重构，支持激光笔精准瞄准")
    print("")

    try:
        # 创建并启动激光瞄准系统
        laser_system = LaserAimingSystem()
        laser_system.run()

    except Exception as e:
        print(f"系统运行异常: {e}")
        print("请检查硬件连接和配置")

# 原有主程序函数 (已废弃，仅供参考)
def main_legacy():
    """
    原有主程序函数 - 已废弃

    这是原有的单体架构实现，已被新的模块化架构替代。
    保留此函数仅供参考和向后兼容性测试。

    新版本的优势:
    1. 模块化设计，便于维护和扩展
    2. 专门的黑色矩形检测算法
    3. 完整的PID控制系统
    4. 激光笔校准功能
    5. 更好的错误处理和状态管理
    6. 性能监控和调试功能

    如需使用原有实现，请调用此函数，但不推荐。
    """
    print("警告: main_legacy()函数已废弃")
    print("原有单体架构存在以下问题:")
    print("1. 全局变量耦合严重")
    print("2. PID控制不完整(P=0)")
    print("3. 通用矩形检测，非专用黑色检测")
    print("4. 缺乏激光校准功能")
    print("5. 代码维护困难")
    print("")
    print("请使用新版本 main() 函数")
    print("参考文档: docs/architecture/Architecture_LaserAiming_v1.0.md")

# 程序入口
if __name__ == "__main__":
    main()
