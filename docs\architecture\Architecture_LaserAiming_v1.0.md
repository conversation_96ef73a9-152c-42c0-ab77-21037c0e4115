# 激光瞄准系统架构设计文档

## 版本信息
- **版本**: v1.0
- **日期**: 2025-01-01
- **作者**: 米醋电子工作室
- **项目**: 野火K210激光笔精准瞄准系统

## 1. 项目概述

### 1.1 项目背景
基于野火K210 AI视觉相机的激光笔精准瞄准系统，实现对黑色矩形目标的自动检测与激光笔精确瞄准。

### 1.2 核心功能
- 黑色矩形目标检测与跟踪
- 双轴舵机PID控制
- 激光笔精准瞄准
- 实时图像处理与显示
- 校准与配置管理

## 2. 架构设计

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────┐
│                LaserAimingSystem                        │
│                   (主控制器)                            │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │   Config    │  │VisionProc   │  │ControlSys   │     │
│  │  (配置管理)  │  │ (视觉处理)   │  │ (控制系统)   │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
│  ┌─────────────┐  ┌─────────────┐                      │
│  │HardwareMgr  │  │Calibration  │                      │
│  │ (硬件抽象)   │  │  (校准管理)  │                      │
│  └─────────────┘  └─────────────┘                      │
└─────────────────────────────────────────────────────────┘
```

### 2.2 模块职责

#### 2.2.1 Config (配置管理模块)
- **职责**: 统一管理系统配置参数
- **功能**: 
  - 摄像头配置参数
  - 舵机控制参数
  - PID控制参数
  - 系统运行参数

#### 2.2.2 HardwareManager (硬件抽象层)
- **职责**: 封装硬件操作接口
- **功能**:
  - 摄像头初始化与控制
  - LCD显示管理
  - 串口通信管理
  - 舵机控制接口

#### 2.2.3 VisionProcessor (视觉处理模块)
- **职责**: 图像处理与目标检测
- **功能**:
  - 黑色矩形检测
  - 目标中心计算
  - 图像预处理
  - 调试信息绘制

#### 2.2.4 ControlSystem (控制系统模块)
- **职责**: 运动控制与PID算法
- **功能**:
  - PID控制器实现
  - 舵机位置控制
  - 串口指令处理
  - 控制参数优化

#### 2.2.5 CalibrationManager (校准管理模块)
- **职责**: 激光笔校准与坐标转换
- **功能**:
  - 激光偏移校准
  - 坐标系转换
  - 校准参数管理
  - 精度验证

#### 2.2.6 LaserAimingSystem (主控制器)
- **职责**: 系统集成与流程控制
- **功能**:
  - 模块协调管理
  - 主循环控制
  - 状态监控
  - 错误处理

## 3. 接口设计

### 3.1 模块间接口
```python
# Config接口
class Config:
    @staticmethod
    def get_camera_settings() -> dict
    @staticmethod
    def get_servo_settings() -> dict
    @staticmethod
    def get_pid_params() -> dict

# HardwareManager接口
class HardwareManager:
    def init_camera(self) -> bool
    def init_uart(self) -> bool
    def send_servo_command(self, servo_id: int, position: int, interval: int) -> bool
    def capture_image(self) -> image.Image

# VisionProcessor接口
class VisionProcessor:
    def detect_black_rectangle(self, img: image.Image) -> list
    def calculate_center(self, rectangles: list) -> tuple
    def draw_debug_info(self, img: image.Image, center: tuple, fps: float) -> None

# ControlSystem接口
class ControlSystem:
    def update_servo_positions(self, target_center: tuple) -> None
    def handle_uart_commands(self, command: str) -> None
    def reset_position(self) -> None

# CalibrationManager接口
class CalibrationManager:
    def calibrate_laser_offset(self) -> bool
    def apply_calibration(self, target_x: float, target_y: float) -> tuple
    def save_calibration(self) -> bool
```

## 4. 数据流设计

### 4.1 主要数据流
```
图像采集 → 黑色矩形检测 → 中心点计算 → 坐标校准 → PID控制 → 舵机驱动
    ↓           ↓            ↓          ↓         ↓         ↓
  原始图像   → 检测结果    → 目标坐标  → 校准坐标 → 控制量   → 舵机位置
```

### 4.2 控制流程
1. **初始化阶段**: 硬件初始化 → 参数加载 → 校准检查
2. **运行阶段**: 图像处理 → 目标检测 → 控制计算 → 舵机驱动
3. **校准阶段**: 校准模式 → 偏移计算 → 参数保存

## 5. 性能指标

### 5.1 实时性要求
- **帧率**: ≥15 FPS
- **响应时间**: <100ms
- **控制精度**: ±1像素

### 5.2 稳定性要求
- **检测准确率**: ≥95%
- **系统稳定性**: 24小时连续运行
- **内存使用**: 稳定无泄漏

## 6. 扩展性设计

### 6.1 算法扩展
- 支持多种目标检测算法
- 可配置的图像处理流水线
- 插件式控制算法

### 6.2 硬件扩展
- 支持不同型号舵机
- 可配置的硬件接口
- 多种通信协议支持

## 7. 向后兼容性

### 7.1 接口兼容
- 保持原有硬件初始化流程
- 维护现有串口通信协议
- 保留调试显示格式

### 7.2 配置兼容
- 支持原有参数设置
- 渐进式配置迁移
- 默认参数向后兼容
