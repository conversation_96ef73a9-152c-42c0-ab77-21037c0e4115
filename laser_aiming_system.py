# 野火K210激光瞄准系统主控制器
# 版权所有: 米醋电子工作室

import time
import gc
from config import Config
from hardware_manager import HardwareManager
from vision_processor import VisionProcessor
from control_system import ControlSystem
from calibration_manager import CalibrationManager

class LaserAimingSystem:
    """激光瞄准系统主控制器 - 集成所有功能模块"""
    
    def __init__(self):
        """初始化激光瞄准系统"""
        print("=== 野火K210激光瞄准系统启动 ===")
        
        # 配置管理
        self.config = Config()
        self.config.print_config_summary()
        
        # 验证配置
        config_errors = self.config.validate_config()
        if config_errors:
            print("配置验证失败:")
            for error in config_errors:
                print(f"  - {error}")
            return
        
        # 初始化各功能模块
        self.hardware = HardwareManager()
        self.vision = VisionProcessor()
        self.control = None  # 等待硬件初始化完成后创建
        self.calibration = None
        
        # 系统状态
        self.running = False
        self.system_mode = 'auto'  # 'auto', 'manual', 'calibration'
        self.last_command = None
        
        # 性能监控 - K210兼容性处理
        try:
            import time
            self.clock = time.clock() if hasattr(time, 'clock') else None
        except:
            self.clock = None
        self.frame_count = 0
        self.start_time = time.ticks_ms()
        self.gc_counter = 0
        
        print("主控制器初始化完成")
    
    def initialize_system(self):
        """初始化系统硬件和模块"""
        print("正在初始化系统硬件...")
        
        # 硬件初始化
        if not self.hardware.init_camera():
            print("摄像头初始化失败")
            return False
        
        if not self.hardware.init_lcd():
            print("LCD初始化失败")
            return False
        
        if not self.hardware.init_uart():
            print("串口初始化失败")
            return False
        
        # 创建控制系统和校准管理器
        self.control = ControlSystem(self.hardware)
        self.calibration = CalibrationManager(self.hardware)
        
        # 硬件自检
        if not self.hardware.hardware_self_test():
            print("硬件自检失败")
            return False
        
        # 设置舵机初始位置
        self.control.reset_to_center()
        
        print("系统初始化完成")
        return True
    
    def run(self):
        """主运行循环"""
        if not self.initialize_system():
            print("系统初始化失败，无法启动")
            return
        
        print("激光瞄准系统开始运行...")
        self.running = True
        
        try:
            while self.running:
                self._main_loop_iteration()
                
        except KeyboardInterrupt:
            print("接收到停止信号")
        except Exception as e:
            print(f"系统运行异常: {e}")
        finally:
            self._cleanup_system()
    
    def _main_loop_iteration(self):
        """主循环单次迭代"""
        # 更新时钟
        if self.clock:
            self.clock.tick()
        
        # 垃圾回收
        self._periodic_garbage_collection()
        
        # 图像采集
        img = self.hardware.capture_image()
        if img is None:
            return
        
        # 串口指令处理
        self._handle_uart_commands()
        
        # 根据系统模式执行不同逻辑
        if self.system_mode == 'auto':
            self._auto_mode_processing(img)
        elif self.system_mode == 'calibration':
            self._calibration_mode_processing(img)
        elif self.system_mode == 'manual':
            self._manual_mode_processing(img)
        
        # 显示图像
        self.hardware.display_image(img)
        
        # 更新帧计数
        self.frame_count += 1
    
    def _auto_mode_processing(self, img):
        """自动模式处理"""
        # 图像预处理
        img.laplacian(1, sharpen=True)
        
        # 黑色矩形检测
        rectangles = self.vision.detect_black_rectangle(img)
        
        # 绘制检测结果
        self.vision.draw_detection_results(img, rectangles)
        
        # 计算目标中心
        target_center = self.vision.calculate_center(rectangles)
        
        # 应用校准修正
        if self.calibration and self.calibration.is_calibrated:
            target_center = self.calibration.apply_calibration(
                target_center[0], target_center[1]
            )
        
        # PID控制更新
        if self.control and rectangles:  # 只有检测到目标时才进行控制
            self.control.update_servo_positions(target_center)
        
        # 绘制调试信息
        fps = self.clock.fps() if self.clock else 0
        self.vision.draw_debug_info(img, target_center, fps)
    
    def _calibration_mode_processing(self, img):
        """校准模式处理"""
        # 绘制校准网格
        if self.calibration:
            self.calibration.draw_calibration_grid(img)
        
        # 绘制基本信息
        fps = self.clock.fps() if self.clock else 0
        img.draw_string(0, 0, f"FPS: {fps:.1f}", color=(255, 0, 0), scale=2)
        img.draw_string(0, 20, "Calibration Mode", color=(255, 255, 0), scale=2)
        
        # 绘制中心十字线
        img.draw_cross(80, 60, color=(255, 0, 0), size=10)
    
    def _manual_mode_processing(self, img):
        """手动模式处理"""
        # 绘制基本信息
        fps = self.clock.fps() if self.clock else 0
        img.draw_string(0, 0, f"FPS: {fps:.1f}", color=(255, 0, 0), scale=2)
        img.draw_string(0, 20, "Manual Mode", color=(0, 255, 255), scale=2)
        
        # 显示当前舵机位置
        if self.control:
            status = self.control.get_control_status()
            img.draw_string(0, 40, f"X: {status['servo_x_pos']}", 
                          color=(255, 255, 255), scale=1)
            img.draw_string(0, 60, f"Y: {status['servo_y_pos']}", 
                          color=(255, 255, 255), scale=1)
        
        # 绘制中心十字线
        img.draw_cross(80, 60, color=(0, 255, 0), size=8)
    
    def _handle_uart_commands(self):
        """处理串口指令"""
        command = self.hardware.read_uart_data()
        
        if command and command != self.last_command:
            print(f"接收到串口指令: {command}")
            
            # 系统模式切换指令
            if command == '0':
                self.set_system_mode('auto')
            elif command == '1':
                self.set_system_mode('manual')
            elif command == '9':
                self.set_system_mode('calibration')
            elif command == 'c':
                self.start_calibration()
            elif command == 'r':
                self.reset_system()
            elif command == 'q':
                self.stop_system()
            else:
                # 传递给控制系统处理
                if self.control:
                    self.control.handle_uart_commands(command)
            
            self.last_command = command
    
    def _periodic_garbage_collection(self):
        """定期垃圾回收"""
        self.gc_counter += 1
        gc_interval = self.config.get_system_settings()['gc_interval']
        
        if self.gc_counter >= gc_interval:
            gc.collect()
            self.gc_counter = 0
    
    def set_system_mode(self, mode):
        """设置系统模式"""
        if mode not in ['auto', 'manual', 'calibration']:
            print(f"无效的系统模式: {mode}")
            return
        
        self.system_mode = mode
        
        if self.control:
            if mode == 'auto':
                self.control.enable_control()
            elif mode == 'manual':
                self.control.disable_control()
            elif mode == 'calibration':
                self.control.disable_control()
        
        print(f"系统模式已切换到: {mode}")
    
    def start_calibration(self):
        """启动校准流程"""
        if self.calibration:
            self.set_system_mode('calibration')
            self.calibration.start_calibration_mode()
        else:
            print("校准管理器未初始化")
    
    def reset_system(self):
        """重置系统"""
        print("正在重置系统...")
        
        if self.control:
            self.control.reset_to_center()
        
        if self.vision:
            self.vision.reset_stats()
        
        if self.calibration:
            self.calibration.reset_calibration()
        
        self.set_system_mode('auto')
        print("系统重置完成")
    
    def stop_system(self):
        """停止系统运行"""
        print("正在停止系统...")
        self.running = False
    
    def _cleanup_system(self):
        """清理系统资源"""
        print("正在清理系统资源...")
        
        if self.hardware:
            self.hardware.cleanup()
        
        # 最终垃圾回收
        gc.collect()
        
        # 计算运行统计
        total_time = time.ticks_diff(time.ticks_ms(), self.start_time) / 1000.0
        avg_fps = self.frame_count / total_time if total_time > 0 else 0
        
        print(f"系统运行统计:")
        print(f"  总运行时间: {total_time:.1f}秒")
        print(f"  总帧数: {self.frame_count}")
        print(f"  平均FPS: {avg_fps:.1f}")
        
        print("系统已停止")
    
    def get_system_status(self):
        """获取系统状态"""
        status = {
            'running': self.running,
            'system_mode': self.system_mode,
            'frame_count': self.frame_count,
            'uptime': time.ticks_diff(time.ticks_ms(), self.start_time) / 1000.0
        }
        
        if self.hardware:
            status['hardware'] = self.hardware.get_hardware_status()
        
        if self.vision:
            status['vision'] = self.vision.get_detection_stats()
        
        if self.control:
            status['control'] = self.control.get_control_status()
        
        if self.calibration:
            status['calibration'] = self.calibration.get_calibration_status()
        
        return status

# 程序入口函数
def main():
    """主程序入口"""
    try:
        # 创建激光瞄准系统
        laser_system = LaserAimingSystem()
        
        # 启动系统
        laser_system.run()
        
    except Exception as e:
        print(f"系统启动失败: {e}")

if __name__ == "__main__":
    main()
