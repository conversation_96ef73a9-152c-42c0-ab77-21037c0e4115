# 野火K210激光瞄准系统启动脚本
# 版权所有: 米醋电子工作室
# 简化的启动脚本，适用于K210环境

"""
K210环境启动脚本

这个脚本专门为K210环境设计，包含了必要的错误处理和环境检查。
如果在CanMV IDE中遇到问题，请使用此脚本启动系统。
"""

def check_k210_environment():
    """检查K210环境"""
    try:
        import sensor, image, lcd
        import machine
        from fpioa_manager import fm
        print("✓ K210环境检查通过")
        return True
    except ImportError as e:
        print(f"✗ K210环境检查失败: {e}")
        print("请确保在CanMV IDE或K210设备上运行")
        return False

def safe_import_modules():
    """安全导入模块"""
    modules = {}
    
    try:
        from config import Config
        modules['config'] = Config
        print("✓ 配置模块导入成功")
    except Exception as e:
        print(f"✗ 配置模块导入失败: {e}")
        return None
    
    try:
        from hardware_manager import HardwareManager
        modules['hardware'] = HardwareManager
        print("✓ 硬件管理模块导入成功")
    except Exception as e:
        print(f"✗ 硬件管理模块导入失败: {e}")
        return None
    
    try:
        from laser_aiming_system import LaserAimingSystem
        modules['system'] = LaserAimingSystem
        print("✓ 主系统模块导入成功")
    except Exception as e:
        print(f"✗ 主系统模块导入失败: {e}")
        return None
    
    return modules

def start_system():
    """启动系统"""
    print("=== 野火K210激光瞄准系统启动 ===")
    print("版权所有: 米醋电子工作室")
    print("")
    
    # 检查K210环境
    if not check_k210_environment():
        return False
    
    # 导入模块
    modules = safe_import_modules()
    if modules is None:
        print("模块导入失败，无法启动系统")
        return False
    
    try:
        # 创建并启动激光瞄准系统
        print("正在创建激光瞄准系统...")
        laser_system = modules['system']()
        
        print("正在启动系统...")
        laser_system.run()
        
        return True
        
    except KeyboardInterrupt:
        print("用户中断，系统停止")
        return True
    except Exception as e:
        print(f"系统运行异常: {e}")
        print("请检查硬件连接和配置")
        return False

def simple_test():
    """简单的系统测试"""
    print("\n=== 简单系统测试 ===")
    
    try:
        # 测试配置模块
        from config import Config
        config = Config()
        print("✓ 配置模块测试通过")
        
        # 测试硬件管理模块
        from hardware_manager import HardwareManager
        hardware = HardwareManager()
        print("✓ 硬件管理模块测试通过")
        
        print("✓ 基础模块测试全部通过")
        return True
        
    except Exception as e:
        print(f"✗ 模块测试失败: {e}")
        return False

def main():
    """主函数"""
    try:
        # 运行简单测试
        if not simple_test():
            print("基础测试失败，请检查代码")
            return
        
        # 启动系统
        start_system()
        
    except Exception as e:
        print(f"启动脚本异常: {e}")

if __name__ == "__main__":
    main()
