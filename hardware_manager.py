# 野火K210激光瞄准系统硬件抽象层
# 版权所有: 米醋电子工作室

from machine import UART
import time
import sensor, image, lcd
from fpioa_manager import fm
import gc
from config import Config

class HardwareManager:
    """硬件管理类 - 封装所有硬件操作接口"""
    
    def __init__(self):
        """初始化硬件管理器"""
        self.uart = None
        self.camera_initialized = False
        self.lcd_initialized = False
        self.uart_initialized = False
        self.config = Config()
        
        print("硬件管理器初始化完成")
    
    def init_camera(self):
        """初始化摄像头 - 严格按照野火K210规范"""
        try:
            camera_config = self.config.get_camera_settings()
            
            # 摄像头复位
            sensor.reset()
            
            # 设置像素格式
            if camera_config['pixformat'] == 'GRAYSCALE':
                sensor.set_pixformat(sensor.GRAYSCALE)
            elif camera_config['pixformat'] == 'RGB565':
                sensor.set_pixformat(sensor.RGB565)
            
            # 设置分辨率
            if camera_config['framesize'] == 'QQVGA':
                sensor.set_framesize(sensor.QQVGA)  # 160x120
            elif camera_config['framesize'] == 'QVGA':
                sensor.set_framesize(sensor.QVGA)   # 320x240
            
            # 设置图像参数
            sensor.set_brightness(camera_config['brightness'])
            sensor.set_saturation(camera_config['saturation'])
            
            # 跳过初始帧
            sensor.skip_frames(time=camera_config['skip_frames_time'])
            
            self.camera_initialized = True
            print("摄像头初始化成功")
            return True
            
        except Exception as e:
            print(f"摄像头初始化失败: {e}")
            return False
    
    def init_lcd(self):
        """初始化LCD显示"""
        try:
            lcd_config = self.config.get_system_settings()
            
            # LCD初始化
            lcd.init()
            
            # 清屏
            if hasattr(lcd, 'WHITE'):
                lcd.clear(lcd.WHITE)
            else:
                lcd.clear()
            
            self.lcd_initialized = True
            print("LCD初始化成功")
            return True
            
        except Exception as e:
            print(f"LCD初始化失败: {e}")
            return False
    
    def init_uart(self):
        """初始化串口通信 - 野火K210标准配置"""
        try:
            uart_config = self.config.get_uart_settings()
            
            # 配置引脚映射 - 野火K210标准引脚
            fm.register(uart_config['rx_pin'], fm.fpioa.UART1_RX, force=True)
            fm.register(uart_config['tx_pin'], fm.fpioa.UART1_TX, force=True)
            
            # 初始化UART
            self.uart = UART(
                getattr(UART, f"UART{uart_config['uart_num']}"),
                uart_config['baudrate'],
                read_buf_len=uart_config['read_buf_len']
            )
            
            self.uart_initialized = True
            print(f"串口UART{uart_config['uart_num']}初始化成功")
            return True
            
        except Exception as e:
            print(f"串口初始化失败: {e}")
            return False
    
    def send_servo_command(self, servo_id, position, interval):
        """发送舵机控制指令 - 野火K210串口协议"""
        if not self.uart_initialized or self.uart is None:
            print("串口未初始化")
            return False
        
        try:
            # 限制位置范围
            servo_config = self.config.get_servo_settings()
            position = max(servo_config['min_position'], 
                          min(servo_config['max_position'], position))
            
            # 构建舵机控制协议包
            ZT1 = 0xFF
            ZT2 = 0xFF
            DATA1 = 0X2A
            DATA2 = (position >> 8) & 0xff
            DATA3 = position & 0xff
            DATA4 = (interval >> 8) & 0xff
            DATA5 = interval & 0xff
            data_length = 0x07
            WriteDATA = 0X03
            
            # 计算校验和
            GetChecksum = (~(servo_id + data_length + WriteDATA + 
                           DATA1 + DATA2 + DATA3 + DATA4 + DATA5)) & 0xff
            
            # 组装数据包
            command = bytes([ZT1, ZT2, servo_id, data_length, WriteDATA, 
                           DATA1, DATA2, DATA3, DATA4, DATA5, GetChecksum])
            
            # 发送指令
            self.uart.write(command)
            return True
            
        except Exception as e:
            print(f"舵机控制指令发送失败: {e}")
            return False
    
    def read_uart_data(self):
        """读取串口数据"""
        if not self.uart_initialized or self.uart is None:
            return None
        
        try:
            data = self.uart.read(1)
            if data:
                try:
                    return data.decode('utf-8')
                except:
                    return None
            return None
            
        except Exception as e:
            print(f"串口数据读取失败: {e}")
            return None
    
    def capture_image(self):
        """采集图像"""
        if not self.camera_initialized:
            print("摄像头未初始化")
            return None
        
        try:
            return sensor.snapshot()
        except Exception as e:
            print(f"图像采集失败: {e}")
            return None
    
    def display_image(self, img):
        """显示图像到LCD"""
        if not self.lcd_initialized:
            print("LCD未初始化")
            return False
        
        try:
            lcd.display(img)
            return True
        except Exception as e:
            print(f"图像显示失败: {e}")
            return False
    
    def set_servo_center_position(self):
        """设置舵机到中心位置"""
        servo_config = self.config.get_servo_settings()
        
        success1 = self.send_servo_command(
            servo_config['ID1'], 
            servo_config['center_x'], 
            servo_config['interval']
        )
        
        success2 = self.send_servo_command(
            servo_config['ID2'], 
            servo_config['center_y'], 
            servo_config['interval']
        )
        
        return success1 and success2
    
    def set_servo_left_position(self):
        """设置舵机到左转位置"""
        servo_config = self.config.get_servo_settings()
        
        success1 = self.send_servo_command(
            servo_config['ID1'], 
            servo_config['left_position'], 
            servo_config['interval']
        )
        
        success2 = self.send_servo_command(
            servo_config['ID2'], 
            servo_config['center_y'], 
            servo_config['interval']
        )
        
        return success1 and success2
    
    def set_servo_right_position(self):
        """设置舵机到右转位置"""
        servo_config = self.config.get_servo_settings()
        
        success1 = self.send_servo_command(
            servo_config['ID1'], 
            servo_config['right_position'], 
            servo_config['interval']
        )
        
        success2 = self.send_servo_command(
            servo_config['ID2'], 
            servo_config['center_y'], 
            servo_config['interval']
        )
        
        return success1 and success2
    
    def hardware_self_test(self):
        """硬件自检"""
        print("开始硬件自检...")
        
        results = {
            'camera': self.camera_initialized,
            'lcd': self.lcd_initialized,
            'uart': self.uart_initialized
        }
        
        # 测试舵机响应
        if self.uart_initialized:
            servo_config = self.config.get_servo_settings()
            test_result = self.send_servo_command(
                servo_config['ID1'], 
                servo_config['center_x'], 
                servo_config['interval']
            )
            results['servo'] = test_result
        else:
            results['servo'] = False
        
        # 打印自检结果
        for device, status in results.items():
            status_str = "正常" if status else "异常"
            print(f"{device}: {status_str}")
        
        all_ok = all(results.values())
        print(f"硬件自检{'通过' if all_ok else '失败'}")
        
        return all_ok
    
    def cleanup(self):
        """清理硬件资源"""
        try:
            if self.uart:
                self.uart.deinit()
            
            # 执行垃圾回收
            gc.collect()
            
            print("硬件资源清理完成")
            
        except Exception as e:
            print(f"硬件资源清理失败: {e}")
    
    def get_hardware_status(self):
        """获取硬件状态"""
        return {
            'camera_initialized': self.camera_initialized,
            'lcd_initialized': self.lcd_initialized,
            'uart_initialized': self.uart_initialized,
            'uart_available': self.uart is not None
        }
