# 语法检查脚本
# 用于验证所有Python文件的语法正确性

import ast
import os

def check_syntax(filename):
    """检查单个文件的语法"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 尝试解析AST
        ast.parse(source)
        return True, None
        
    except SyntaxError as e:
        return False, f"语法错误: {e}"
    except Exception as e:
        return False, f"其他错误: {e}"

def main():
    """检查所有Python文件"""
    files_to_check = [
        'main.py',
        'config.py', 
        'hardware_manager.py',
        'vision_processor.py',
        'control_system.py',
        'calibration_manager.py',
        'laser_aiming_system.py',
        'test_system.py'
    ]
    
    print("=== Python文件语法检查 ===")
    
    all_passed = True
    
    for filename in files_to_check:
        if os.path.exists(filename):
            passed, error = check_syntax(filename)
            status = "✓ 通过" if passed else "✗ 失败"
            print(f"{filename}: {status}")
            
            if not passed:
                print(f"  错误详情: {error}")
                all_passed = False
        else:
            print(f"{filename}: ✗ 文件不存在")
            all_passed = False
    
    print("\n=== 检查结果 ===")
    if all_passed:
        print("🎉 所有文件语法检查通过！")
    else:
        print("⚠️ 发现语法错误，请修复后重试")
    
    return all_passed

if __name__ == "__main__":
    main()
