# -*- coding: utf-8 -*-
# 野火K210激光瞄准系统视觉处理模块
# 版权所有: 米醋电子工作室

import image
import time
from config import Config

class VisionProcessor:
    """视觉处理类 - 专门处理黑色矩形检测与跟踪"""
    
    def __init__(self):
        """初始化视觉处理器"""
        self.config = Config()
        self.vision_config = self.config.get_vision_settings()
        self.lcd_config = self.config.get_system_settings()
        
        # 目标跟踪状态
        self.last_center = (80, 60)  # 默认图像中心
        self.target_found = False
        self.lost_frames = 0
        self.max_lost_frames = 10
        
        # 性能统计
        self.detection_count = 0
        self.success_count = 0
        
        print("视觉处理器初始化完成")
    
    def detect_black_rectangle(self, img):
        """检测黑色矩形目标 - 优化的检测算法"""
        if img is None:
            return []
        
        try:
            self.detection_count += 1
            rectangles = []
            
            # 方法1: 使用颜色阈值检测黑色区域
            black_blobs = self._detect_black_blobs(img)
            if black_blobs:
                rectangles.extend(self._filter_rectangular_blobs(black_blobs))
            
            # 方法2: 使用边缘检测 + 矩形检测 (备用方法)
            if not rectangles:
                rectangles = self._detect_edge_rectangles(img)
            
            # 过滤和验证检测结果
            valid_rectangles = self._validate_rectangles(rectangles)
            
            if valid_rectangles:
                self.success_count += 1
                self.target_found = True
                self.lost_frames = 0
            else:
                self.lost_frames += 1
                if self.lost_frames > self.max_lost_frames:
                    self.target_found = False
            
            return valid_rectangles
            
        except Exception as e:
            print(f"黑色矩形检测失败: {e}")
            return []
    
    def _detect_black_blobs(self, img):
        """使用颜色阈值检测黑色区域"""
        try:
            # 创建二值图像 - 检测黑色区域
            threshold = self.vision_config['black_threshold']
            binary_img = img.binary([threshold])
            
            # 形态学处理去除噪声
            morph_size = self.vision_config['morphology_size']
            binary_img.erode(morph_size).dilate(morph_size)
            
            # 查找黑色区域
            min_area = self.vision_config['min_area']
            max_area = self.vision_config['max_area']
            
            blobs = binary_img.find_blobs(
                [threshold], 
                area_threshold=min_area,
                pixels_threshold=min_area
            )
            
            # 过滤面积过大的区域
            filtered_blobs = [blob for blob in blobs if blob.area() <= max_area]
            
            return filtered_blobs
            
        except Exception as e:
            print(f"黑色区域检测失败: {e}")
            return []
    
    def _filter_rectangular_blobs(self, blobs):
        """过滤出矩形状的区域"""
        rectangles = []
        
        for blob in blobs:
            try:
                # 计算矩形度 (面积比)
                rect_area = blob.w() * blob.h()
                if rect_area > 0:
                    rectangularity = blob.area() / rect_area
                    
                    # 检查宽高比
                    aspect_ratio = blob.w() / blob.h() if blob.h() > 0 else 0
                    
                    # 矩形度和宽高比过滤
                    if (rectangularity > 0.6 and  # 矩形度阈值
                        self.vision_config['aspect_ratio_min'] <= aspect_ratio <= 
                        self.vision_config['aspect_ratio_max']):
                        
                        # 构造矩形对象
                        rect = {
                            'x': blob.x(),
                            'y': blob.y(),
                            'w': blob.w(),
                            'h': blob.h(),
                            'area': blob.area(),
                            'rectangularity': rectangularity,
                            'aspect_ratio': aspect_ratio
                        }
                        rectangles.append(rect)
                        
            except Exception as e:
                print(f"矩形过滤失败: {e}")
                continue
        
        return rectangles
    
    def _detect_edge_rectangles(self, img):
        """使用边缘检测方法检测矩形 - 备用方法"""
        try:
            # 拉普拉斯边缘检测
            img_copy = img.copy()
            img_copy.laplacian(1, sharpen=True)
            
            # 矩形检测
            threshold = self.vision_config['rect_threshold']
            rects = img_copy.find_rects(threshold=threshold)
            
            rectangles = []
            for r in rects:
                rect = {
                    'x': r.x(),
                    'y': r.y(),
                    'w': r.w(),
                    'h': r.h(),
                    'area': r.w() * r.h(),
                    'rectangularity': 1.0,  # 边缘检测的矩形度默认为1
                    'aspect_ratio': r.w() / r.h() if r.h() > 0 else 0
                }
                rectangles.append(rect)
            
            return rectangles
            
        except Exception as e:
            print(f"边缘矩形检测失败: {e}")
            return []
    
    def _validate_rectangles(self, rectangles):
        """验证和排序矩形检测结果"""
        if not rectangles:
            return []
        
        valid_rects = []
        
        for rect in rectangles:
            # 面积验证
            if (self.vision_config['min_area'] <= rect['area'] <= 
                self.vision_config['max_area']):
                
                # 位置验证 (确保在图像范围内)
                if (rect['x'] >= 0 and rect['y'] >= 0 and 
                    rect['x'] + rect['w'] <= 160 and  # QQVGA宽度
                    rect['y'] + rect['h'] <= 120):    # QQVGA高度
                    
                    valid_rects.append(rect)
        
        # 按面积排序，优先选择较大的目标
        valid_rects.sort(key=lambda r: r['area'], reverse=True)
        
        return valid_rects
    
    def calculate_center(self, rectangles):
        """计算目标中心点 - 带滤波的中心计算"""
        if not rectangles:
            return self.last_center
        
        try:
            # 选择最大的矩形作为主要目标
            main_rect = rectangles[0]
            
            # 计算几何中心
            center_x = main_rect['x'] + main_rect['w'] // 2
            center_y = main_rect['y'] + main_rect['h'] // 2
            
            # 应用低通滤波平滑中心点
            alpha = self.vision_config['center_filter_alpha']
            filtered_x = alpha * center_x + (1 - alpha) * self.last_center[0]
            filtered_y = alpha * center_y + (1 - alpha) * self.last_center[1]
            
            new_center = (int(filtered_x), int(filtered_y))
            self.last_center = new_center
            
            return new_center
            
        except Exception as e:
            print(f"中心点计算失败: {e}")
            return self.last_center
    
    def draw_debug_info(self, img, center, fps):
        """绘制调试信息"""
        if img is None:
            return
        
        try:
            lcd_config = self.config.get_system_settings()
            
            if lcd_config['debug_mode']:
                # 绘制FPS信息
                img.draw_string(0, 0, f"FPS: {fps:.1f}", 
                              color=(255, 0, 0), scale=2)
                
                # 绘制系统标识
                img.draw_string(0, 20, "WildFire K210", 
                              color=(0, 255, 0), scale=2)
                img.draw_string(0, 40, "Laser Aiming", 
                              color=(0, 0, 255), scale=2)
                
                # 绘制目标状态
                status = "FOUND" if self.target_found else "LOST"
                color = (0, 255, 0) if self.target_found else (255, 0, 0)
                img.draw_string(0, 60, f"Target: {status}", 
                              color=color, scale=1)
                
                # 绘制检测统计
                if self.detection_count > 0:
                    success_rate = (self.success_count / self.detection_count) * 100
                    img.draw_string(0, 80, f"Rate: {success_rate:.1f}%", 
                                  color=(255, 255, 0), scale=1)
                
                # 绘制中心十字线
                img.draw_cross(center[0], center[1], 
                             color=(255, 0, 0), size=10)
                
                # 绘制图像中心参考线
                img.draw_cross(80, 60, color=(0, 255, 255), size=5)
                
        except Exception as e:
            print(f"调试信息绘制失败: {e}")
    
    def draw_detection_results(self, img, rectangles):
        """绘制检测结果"""
        if img is None or not rectangles:
            return
        
        try:
            for i, rect in enumerate(rectangles):
                # 绘制矩形边框
                color = (255, 0, 0) if i == 0 else (0, 255, 0)  # 主目标红色，其他绿色
                img.draw_rectangle(rect['x'], rect['y'], rect['w'], rect['h'], 
                                 color=color, thickness=2)
                
                # 绘制中心点
                center_x = rect['x'] + rect['w'] // 2
                center_y = rect['y'] + rect['h'] // 2
                img.draw_circle(center_x, center_y, 3, color=color, fill=True)
                
                # 绘制面积信息
                if self.config.get_system_settings()['debug_mode']:
                    img.draw_string(rect['x'], rect['y'] - 10, 
                                  f"A:{rect['area']}", 
                                  color=color, scale=1)
                    
        except Exception as e:
            print(f"检测结果绘制失败: {e}")
    
    def get_detection_stats(self):
        """获取检测统计信息"""
        if self.detection_count == 0:
            return {
                'detection_count': 0,
                'success_count': 0,
                'success_rate': 0.0,
                'target_found': False
            }
        
        return {
            'detection_count': self.detection_count,
            'success_count': self.success_count,
            'success_rate': (self.success_count / self.detection_count) * 100,
            'target_found': self.target_found,
            'lost_frames': self.lost_frames
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.detection_count = 0
        self.success_count = 0
        self.lost_frames = 0
        self.target_found = False
        print("视觉处理统计信息已重置")
