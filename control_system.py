# -*- coding: utf-8 -*-
# 野火K210激光瞄准系统控制模块
# 版权所有: 米醋电子工作室

import time
from config import Config

class PIDController:
    """PID控制器类 - 完整的PID算法实现"""
    
    def __init__(self, kp, ki, kd, setpoint=0, output_limit=None):
        """初始化PID控制器"""
        self.kp = kp  # 比例系数
        self.ki = ki  # 积分系数
        self.kd = kd  # 微分系数
        self.setpoint = setpoint  # 目标值
        self.output_limit = output_limit  # 输出限制
        
        # 内部状态变量
        self.integral = 0.0
        self.last_error = 0.0
        self.last_time = time.ticks_ms()
        self.last_output = 0.0
        
        # 积分限制和微分滤波
        self.integral_limit = 1000
        self.derivative_filter = 0.1
        self.filtered_derivative = 0.0
        
    def update(self, current_value, dt=None):
        """更新PID控制器"""
        current_time = time.ticks_ms()
        
        if dt is None:
            dt = time.ticks_diff(current_time, self.last_time) / 1000.0  # 转换为秒
            if dt <= 0:
                dt = 0.001  # 最小时间间隔
        
        # 计算误差
        error = self.setpoint - current_value
        
        # 比例项
        proportional = self.kp * error
        
        # 积分项 (带积分限制)
        self.integral += error * dt
        if self.integral_limit:
            self.integral = max(-self.integral_limit, 
                              min(self.integral_limit, self.integral))
        integral_term = self.ki * self.integral
        
        # 微分项 (带滤波)
        derivative = (error - self.last_error) / dt if dt > 0 else 0
        self.filtered_derivative = (self.derivative_filter * derivative + 
                                  (1 - self.derivative_filter) * self.filtered_derivative)
        derivative_term = self.kd * self.filtered_derivative
        
        # PID输出
        output = proportional + integral_term + derivative_term
        
        # 输出限制
        if self.output_limit:
            output = max(-self.output_limit, min(self.output_limit, output))
        
        # 更新状态
        self.last_error = error
        self.last_time = current_time
        self.last_output = output
        
        return output
    
    def reset(self):
        """重置PID控制器状态"""
        self.integral = 0.0
        self.last_error = 0.0
        self.last_time = time.ticks_ms()
        self.filtered_derivative = 0.0
    
    def set_tunings(self, kp, ki, kd):
        """设置PID参数"""
        self.kp = kp
        self.ki = ki
        self.kd = kd
    
    def set_setpoint(self, setpoint):
        """设置目标值"""
        self.setpoint = setpoint
    
    def get_components(self):
        """获取PID各分量"""
        return {
            'proportional': self.kp * self.last_error,
            'integral': self.ki * self.integral,
            'derivative': self.kd * self.filtered_derivative,
            'output': self.last_output
        }

class ControlSystem:
    """控制系统类 - 管理双轴PID控制和舵机驱动"""
    
    def __init__(self, hardware_manager):
        """初始化控制系统"""
        self.hardware = hardware_manager
        self.config = Config()
        
        # 获取配置参数
        pid_config = self.config.get_pid_params()
        servo_config = self.config.get_servo_settings()
        
        # 初始化双轴PID控制器
        x_params = pid_config['x_axis']
        y_params = pid_config['y_axis']
        
        self.pid_x = PIDController(
            x_params['kp'], x_params['ki'], x_params['kd'],
            x_params['setpoint'], x_params['output_limit']
        )
        
        self.pid_y = PIDController(
            y_params['kp'], y_params['ki'], y_params['kd'],
            y_params['setpoint'], y_params['output_limit']
        )
        
        # 舵机位置状态
        self.servo_x_pos = servo_config['center_x']
        self.servo_y_pos = servo_config['center_y']
        
        # 死区控制
        self.deadzone = pid_config['deadzone']
        
        # 控制状态
        self.control_enabled = True
        self.manual_mode = False
        
        # 性能统计
        self.control_count = 0
        self.last_update_time = time.ticks_ms()
        
        print("控制系统初始化完成")
    
    def update_servo_positions(self, target_center):
        """更新舵机位置 - 基于目标中心点"""
        if not self.control_enabled:
            return
        
        try:
            target_x, target_y = target_center
            
            # 死区控制 - 避免小幅抖动
            error_x = abs(target_x - self.pid_x.setpoint)
            error_y = abs(target_y - self.pid_y.setpoint)
            
            if error_x < self.deadzone and error_y < self.deadzone:
                return  # 在死区内，不进行控制
            
            # 计算PID输出
            pid_output_x = self.pid_x.update(target_x)
            pid_output_y = self.pid_y.update(target_y)
            
            # 更新舵机位置
            servo_config = self.config.get_servo_settings()
            
            self.servo_x_pos = int(servo_config['center_x'] - pid_output_x)
            self.servo_y_pos = int(servo_config['center_y'] - pid_output_y)
            
            # 位置限制
            self.servo_x_pos = max(servo_config['min_position'], 
                                 min(servo_config['max_position'], self.servo_x_pos))
            self.servo_y_pos = max(servo_config['min_position'], 
                                 min(servo_config['max_position'], self.servo_y_pos))
            
            # 发送双舵机控制指令（单线模式）
            success = self.hardware.send_dual_servo_command(
                self.servo_x_pos,
                self.servo_y_pos,
                servo_config['interval']
            )

            if success:
                self.control_count += 1
            
        except Exception as e:
            print(f"舵机位置更新失败: {e}")
    
    def handle_uart_commands(self, command):
        """处理串口指令"""
        if not command:
            return
        
        try:
            command_map = self.config.UART_COMMANDS
            
            if command in command_map:
                action = command_map[command]
                
                if action == 'center':
                    self.reset_to_center()
                elif action == 'left':
                    self.move_to_left()
                elif action == 'right':
                    self.move_to_right()
                
                print(f"执行串口指令: {command} -> {action}")
                
        except Exception as e:
            print(f"串口指令处理失败: {e}")
    
    def reset_to_center(self):
        """重置到中心位置"""
        servo_config = self.config.get_servo_settings()
        
        self.servo_x_pos = servo_config['center_x']
        self.servo_y_pos = servo_config['center_y']
        
        # 重置PID状态
        self.pid_x.reset()
        self.pid_y.reset()
        
        # 发送双舵机指令（单线模式）
        self.hardware.send_dual_servo_command(
            self.servo_x_pos,
            self.servo_y_pos,
            servo_config['interval']
        )
        
        time.sleep(1)  # 等待舵机到位
        print("舵机已重置到中心位置")
    
    def move_to_left(self):
        """移动到左转位置"""
        servo_config = self.config.get_servo_settings()
        
        self.servo_x_pos = servo_config['left_position']
        self.servo_y_pos = servo_config['center_y']
        
        # 重置PID状态
        self.pid_x.reset()
        self.pid_y.reset()
        
        # 发送双舵机指令（单线模式）
        self.hardware.send_dual_servo_command(
            self.servo_x_pos,
            self.servo_y_pos,
            servo_config['interval']
        )
        
        time.sleep(1)  # 等待舵机到位
        print("舵机已移动到左转位置")
    
    def move_to_right(self):
        """移动到右转位置"""
        servo_config = self.config.get_servo_settings()
        
        self.servo_x_pos = servo_config['right_position']
        self.servo_y_pos = servo_config['center_y']
        
        # 重置PID状态
        self.pid_x.reset()
        self.pid_y.reset()
        
        # 发送双舵机指令（单线模式）
        self.hardware.send_dual_servo_command(
            self.servo_x_pos,
            self.servo_y_pos,
            servo_config['interval']
        )
        
        time.sleep(1)  # 等待舵机到位
        print("舵机已移动到右转位置")
    
    def enable_control(self):
        """启用自动控制"""
        self.control_enabled = True
        print("自动控制已启用")
    
    def disable_control(self):
        """禁用自动控制"""
        self.control_enabled = False
        print("自动控制已禁用")
    
    def set_manual_mode(self, enabled):
        """设置手动模式"""
        self.manual_mode = enabled
        if enabled:
            self.disable_control()
            print("切换到手动模式")
        else:
            self.enable_control()
            print("切换到自动模式")
    
    def tune_pid_parameters(self, axis, kp=None, ki=None, kd=None):
        """调整PID参数"""
        try:
            if axis == 'x':
                pid = self.pid_x
            elif axis == 'y':
                pid = self.pid_y
            else:
                print("无效的轴参数")
                return
            
            if kp is not None:
                pid.kp = kp
            if ki is not None:
                pid.ki = ki
            if kd is not None:
                pid.kd = kd
            
            print(f"{axis}轴PID参数已更新: Kp={pid.kp}, Ki={pid.ki}, Kd={pid.kd}")
            
        except Exception as e:
            print(f"PID参数调整失败: {e}")
    
    def get_control_status(self):
        """获取控制状态"""
        return {
            'control_enabled': self.control_enabled,
            'manual_mode': self.manual_mode,
            'servo_x_pos': self.servo_x_pos,
            'servo_y_pos': self.servo_y_pos,
            'control_count': self.control_count,
            'pid_x_components': self.pid_x.get_components(),
            'pid_y_components': self.pid_y.get_components()
        }
    
    def reset_control_stats(self):
        """重置控制统计"""
        self.control_count = 0
        self.last_update_time = time.ticks_ms()
        print("控制统计已重置")
