# 野火K210激光瞄准系统测试程序
# 版权所有: 米醋电子工作室

"""
系统功能测试程序

用于验证各个模块的功能是否正常工作。
在CanMV IDE中运行此文件进行系统测试。
"""

import time

def test_config():
    """测试配置管理模块"""
    print("=== 测试配置管理模块 ===")
    
    try:
        from config import Config
        
        # 测试配置获取
        camera_config = Config.get_camera_settings()
        servo_config = Config.get_servo_settings()
        pid_config = Config.get_pid_params()
        
        print(f"摄像头配置: {camera_config['pixformat']} {camera_config['framesize']}")
        print(f"舵机配置: ID1={servo_config['ID1']}, ID2={servo_config['ID2']}")
        print(f"PID配置: Kp={pid_config['x_axis']['kp']}")
        
        # 测试配置验证
        errors = Config.validate_config()
        if errors:
            print(f"配置验证失败: {errors}")
            return False
        else:
            print("配置验证通过")
        
        # 打印配置摘要
        Config.print_config_summary()
        
        print("配置管理模块测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"配置管理模块测试失败: {e}")
        return False

def test_hardware():
    """测试硬件抽象层"""
    print("\n=== 测试硬件抽象层 ===")
    
    try:
        from hardware_manager import HardwareManager
        
        # 创建硬件管理器
        hardware = HardwareManager()
        
        # 测试硬件初始化
        print("初始化摄像头...")
        if not hardware.init_camera():
            print("摄像头初始化失败")
            return False
        
        print("初始化LCD...")
        if not hardware.init_lcd():
            print("LCD初始化失败")
            return False
        
        print("初始化串口...")
        if not hardware.init_uart():
            print("串口初始化失败")
            return False
        
        # 测试硬件自检
        print("执行硬件自检...")
        if not hardware.hardware_self_test():
            print("硬件自检失败")
            return False
        
        # 测试舵机控制
        print("测试舵机控制...")
        if not hardware.set_servo_center_position():
            print("舵机控制失败")
            return False
        
        print("硬件抽象层测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"硬件抽象层测试失败: {e}")
        return False

def test_vision():
    """测试视觉处理模块"""
    print("\n=== 测试视觉处理模块 ===")
    
    try:
        from vision_processor import VisionProcessor
        from hardware_manager import HardwareManager
        
        # 创建视觉处理器
        vision = VisionProcessor()
        hardware = HardwareManager()
        
        # 初始化摄像头
        if not hardware.init_camera():
            print("摄像头初始化失败")
            return False
        
        # 测试图像采集和处理
        print("测试图像处理...")
        for i in range(5):
            img = hardware.capture_image()
            if img is None:
                print(f"图像采集失败 (第{i+1}次)")
                continue
            
            # 测试黑色矩形检测
            rectangles = vision.detect_black_rectangle(img)
            center = vision.calculate_center(rectangles)
            
            print(f"第{i+1}次检测: 发现{len(rectangles)}个目标, 中心点{center}")
            
            # 绘制调试信息
            vision.draw_debug_info(img, center, 15.0)
            vision.draw_detection_results(img, rectangles)
            
            time.sleep(0.5)
        
        # 获取检测统计
        stats = vision.get_detection_stats()
        print(f"检测统计: {stats}")
        
        print("视觉处理模块测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"视觉处理模块测试失败: {e}")
        return False

def test_control():
    """测试控制系统模块"""
    print("\n=== 测试控制系统模块 ===")
    
    try:
        from control_system import ControlSystem, PIDController
        from hardware_manager import HardwareManager
        
        # 创建硬件管理器
        hardware = HardwareManager()
        if not hardware.init_uart():
            print("串口初始化失败")
            return False
        
        # 创建控制系统
        control = ControlSystem(hardware)
        
        # 测试PID控制器
        print("测试PID控制器...")
        pid = PIDController(1.0, 0.1, 0.05, setpoint=80)
        
        # 模拟控制过程
        for i in range(5):
            current_value = 70 + i * 2  # 模拟目标位置变化
            output = pid.update(current_value)
            print(f"PID测试 {i+1}: 当前值={current_value}, 输出={output:.2f}")
        
        # 测试舵机控制
        print("测试舵机控制...")
        control.reset_to_center()
        time.sleep(1)
        
        # 测试串口指令处理
        print("测试串口指令处理...")
        test_commands = ['2', '3', '4', '5']
        for cmd in test_commands:
            print(f"处理指令: {cmd}")
            control.handle_uart_commands(cmd)
            time.sleep(1)
        
        # 获取控制状态
        status = control.get_control_status()
        print(f"控制状态: {status}")
        
        print("控制系统模块测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"控制系统模块测试失败: {e}")
        return False

def test_calibration():
    """测试校准管理模块"""
    print("\n=== 测试校准管理模块 ===")
    
    try:
        from calibration_manager import CalibrationManager
        from hardware_manager import HardwareManager
        
        # 创建硬件管理器
        hardware = HardwareManager()
        if not hardware.init_uart():
            print("串口初始化失败")
            return False
        
        # 创建校准管理器
        calibration = CalibrationManager(hardware)
        
        # 测试校准参数加载
        print("测试校准参数...")
        status = calibration.get_calibration_status()
        print(f"校准状态: {status}")
        
        # 测试坐标转换
        print("测试坐标转换...")
        test_points = [(80, 60), (40, 30), (120, 90)]
        for point in test_points:
            corrected = calibration.apply_calibration(point[0], point[1])
            print(f"原始坐标{point} -> 校准后{corrected}")
        
        print("校准管理模块测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"校准管理模块测试失败: {e}")
        return False

def test_main_system():
    """测试主系统集成"""
    print("\n=== 测试主系统集成 ===")
    
    try:
        from laser_aiming_system import LaserAimingSystem
        
        # 创建激光瞄准系统
        print("创建激光瞄准系统...")
        laser_system = LaserAimingSystem()
        
        # 测试系统初始化
        print("测试系统初始化...")
        if not laser_system.initialize_system():
            print("系统初始化失败")
            return False
        
        # 测试系统状态
        print("获取系统状态...")
        status = laser_system.get_system_status()
        print(f"系统状态: 运行={status['running']}, 模式={status['system_mode']}")
        
        # 测试模式切换
        print("测试模式切换...")
        laser_system.set_system_mode('manual')
        laser_system.set_system_mode('auto')
        
        print("主系统集成测试通过 ✓")
        return True
        
    except Exception as e:
        print(f"主系统集成测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=== 野火K210激光瞄准系统功能测试 ===")
    print("版权所有: 米醋电子工作室")
    print("")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("配置管理", test_config()))
    test_results.append(("硬件抽象层", test_hardware()))
    test_results.append(("视觉处理", test_vision()))
    test_results.append(("控制系统", test_control()))
    test_results.append(("校准管理", test_calibration()))
    test_results.append(("主系统集成", test_main_system()))
    
    # 汇总测试结果
    print("\n=== 测试结果汇总 ===")
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "通过" if result else "失败"
        symbol = "✓" if result else "✗"
        print(f"{test_name}: {status} {symbol}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
    
    return passed == total

if __name__ == "__main__":
    run_all_tests()
