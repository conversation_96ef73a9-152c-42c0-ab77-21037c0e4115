# 野火K210激光瞄准系统

基于野火K210 AI视觉相机的激光笔精准瞄准系统，采用模块化低耦合架构设计，实现对黑色矩形目标的自动检测与激光笔精确瞄准。

## 🎯 项目特性

- **精准瞄准**: 激光笔光点精确打击黑色矩形框中心
- **智能检测**: 专门优化的黑色矩形检测算法
- **PID控制**: 完整的双轴PID控制系统，响应快速稳定
- **自动校准**: 激光笔偏移自动校准系统
- **模块化架构**: 低耦合设计，便于扩展和维护
- **实时处理**: 15+ FPS实时图像处理和控制

## 🏗️ 系统架构

```
LaserAimingSystem (主控制器)
├── Config (配置管理)
├── HardwareManager (硬件抽象层)
├── VisionProcessor (视觉处理)
├── ControlSystem (控制系统)
└── CalibrationManager (校准管理)
```

## 📋 硬件要求

- 野火K210 AI视觉相机
- 双轴舵机云台
- 激光笔模块
- 串口通信连接
- 稳定的电源供应

## 🚀 快速开始

### 1. 硬件连接

1. 将K210相机固定在双轴舵机云台上
2. 在相机上绑定激光笔模块
3. 连接串口通信线缆（引脚0-TX, 引脚1-RX）
4. 确保电源供应充足

### 2. 软件部署

1. 在CanMV IDE中打开项目
2. 将所有.py文件上传到K210设备
3. 运行main.py启动系统

```python
# 直接运行主程序
python main.py

# 或运行系统测试
python test_system.py
```

### 3. 系统操作

#### 串口指令控制

| 指令 | 功能 | 说明 |
|------|------|------|
| `0` | 自动模式 | 自动检测目标并控制激光瞄准 |
| `1` | 手动模式 | 手动控制舵机位置 |
| `9` | 校准模式 | 进行激光笔偏移校准 |
| `2`,`4` | 回中位 | 舵机回到中心位置 |
| `3` | 左转 | 舵机向左转动 |
| `5` | 右转 | 舵机向右转动 |
| `c` | 开始校准 | 启动校准流程 |
| `r` | 重置系统 | 重置所有参数 |
| `q` | 退出程序 | 安全退出系统 |

#### 校准流程

1. 发送 `c` 指令进入校准模式
2. 系统依次移动到9个校准点
3. 在每个点手动调整激光对准目标中心
4. 完成所有点后系统自动计算校准参数
5. 校准参数自动保存，下次启动时加载

## 📁 项目结构

```
├── main.py                    # 主程序入口
├── config.py                  # 配置管理模块
├── hardware_manager.py        # 硬件抽象层
├── vision_processor.py        # 视觉处理模块
├── control_system.py          # 控制系统模块
├── calibration_manager.py     # 校准管理模块
├── laser_aiming_system.py     # 主控制器
├── test_system.py             # 系统测试程序
├── README.md                  # 项目说明
└── docs/                      # 文档目录
    └── architecture/           # 架构设计文档
        └── Architecture_LaserAiming_v1.0.md
```

## ⚙️ 配置参数

### 摄像头配置
- 分辨率: QQVGA (160x120)
- 格式: GRAYSCALE
- 亮度/饱和度: -2

### PID控制参数
- X轴: Kp=0.8, Ki=0.2, Kd=0.1
- Y轴: Kp=0.8, Ki=0.2, Kd=0.1
- 死区: 2像素

### 检测参数
- 黑色阈值: (0, 50)
- 最小面积: 500像素
- 最大面积: 5000像素

## 🔧 性能指标

- **检测精度**: 95%以上准确率
- **控制精度**: 激光抖动 < 1像素
- **响应时间**: < 100ms
- **帧率**: 15+ FPS
- **校准精度**: 误差 < 1像素

## 🐛 故障排除

### 常见问题

1. **摄像头无图像**
   - 检查摄像头连接
   - 确认电源供应
   - 重启系统

2. **舵机不响应**
   - 检查串口连接
   - 验证引脚配置
   - 检查舵机电源

3. **检测不准确**
   - 确保目标为纯黑色
   - 改善光照条件
   - 重新校准系统

4. **激光偏移**
   - 运行校准程序
   - 检查激光笔固定
   - 调整校准参数

### 调试模式

```python
# 启用调试模式
Config.SYSTEM_SETTINGS['debug_mode'] = True

# 查看系统状态
laser_system.get_system_status()

# 查看检测统计
vision.get_detection_stats()
```

## 📚 开发文档

- [架构设计文档](docs/architecture/Architecture_LaserAiming_v1.0.md)
- [API接口文档](docs/api/)
- [用户使用指南](docs/user_guide/)
- [开发者指南](docs/developer_guide/)

## 🔄 版本历史

### v2.0 (当前版本)
- ✅ 模块化架构重构
- ✅ 专用黑色矩形检测算法
- ✅ 完整PID控制系统
- ✅ 激光笔校准功能
- ✅ 性能监控和调试

### v1.0 (原始版本)
- ✅ 基础目标跟踪
- ✅ 简单舵机控制
- ❌ 单体架构，难以维护
- ❌ PID控制不完整

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

版权所有 © 2025 米醋电子工作室

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 技术支持

- 项目主页: [GitHub Repository]
- 技术文档: [Documentation]
- 问题反馈: [Issues]
- 邮箱支持: <EMAIL>

---

**🎉 感谢使用野火K210激光瞄准系统！**
