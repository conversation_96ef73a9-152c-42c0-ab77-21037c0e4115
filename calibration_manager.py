# -*- coding: utf-8 -*-
# 野火K210激光瞄准系统校准管理模块
# 版权所有: 米醋电子工作室

import time
import json
from config import Config

class CalibrationManager:
    """校准管理类 - 激光笔偏移校准与坐标转换"""
    
    def __init__(self, hardware_manager):
        """初始化校准管理器"""
        self.hardware = hardware_manager
        self.config = Config()
        self.calib_config = self.config.get_calibration_settings()
        
        # 校准参数
        self.offset_x = self.calib_config['offset_x']
        self.offset_y = self.calib_config['offset_y']
        self.scale_x = self.calib_config['scale_x']
        self.scale_y = self.calib_config['scale_y']
        
        # 校准状态
        self.is_calibrated = False
        self.calibration_mode = False
        self.calibration_points = []
        self.current_point_index = 0
        
        # 校准网格点 (3x3)
        self.grid_points = [
            (40, 30), (80, 30), (120, 30),   # 上排
            (40, 60), (80, 60), (120, 60),   # 中排
            (40, 90), (80, 90), (120, 90)    # 下排
        ]
        
        # 加载已保存的校准参数
        self.load_calibration()
        
        print("校准管理器初始化完成")
    
    def start_calibration_mode(self):
        """启动校准模式"""
        print("启动激光笔校准模式...")
        self.calibration_mode = True
        self.current_point_index = 0
        self.calibration_points = []
        
        # 移动到第一个校准点
        self._move_to_calibration_point(0)
        
        print("校准模式已启动，请按照提示进行校准")
        return True
    
    def _move_to_calibration_point(self, point_index):
        """移动到指定校准点"""
        if point_index >= len(self.grid_points):
            return False
        
        target_x, target_y = self.grid_points[point_index]
        
        # 计算舵机位置 (简单线性映射)
        servo_config = self.config.get_servo_settings()
        
        # 图像坐标到舵机位置的映射
        # X轴: 0-160像素 -> min_position到max_position
        servo_x = int(servo_config['min_position'] + 
                     (target_x / 160.0) * 
                     (servo_config['max_position'] - servo_config['min_position']))
        
        # Y轴: 0-120像素 -> min_position到max_position
        servo_y = int(servo_config['min_position'] + 
                     (target_y / 120.0) * 
                     (servo_config['max_position'] - servo_config['min_position']))
        
        # 发送双舵机控制指令（单线模式）
        success = self.hardware.send_dual_servo_command(
            servo_x, servo_y, servo_config['interval']
        )

        if success:
            print(f"移动到校准点 {point_index + 1}: ({target_x}, {target_y})")
            time.sleep(2)  # 等待舵机稳定
            return True
        else:
            print(f"移动到校准点 {point_index + 1} 失败")
            return False
    
    def capture_calibration_point(self, actual_laser_pos):
        """捕获校准点 - 记录激光实际位置"""
        if not self.calibration_mode:
            print("未处于校准模式")
            return False
        
        if self.current_point_index >= len(self.grid_points):
            print("校准已完成")
            return False
        
        # 记录校准数据
        target_pos = self.grid_points[self.current_point_index]
        calibration_data = {
            'target': target_pos,
            'actual': actual_laser_pos,
            'error_x': actual_laser_pos[0] - target_pos[0],
            'error_y': actual_laser_pos[1] - target_pos[1]
        }
        
        self.calibration_points.append(calibration_data)
        
        print(f"校准点 {self.current_point_index + 1} 已记录:")
        print(f"  目标位置: {target_pos}")
        print(f"  实际位置: {actual_laser_pos}")
        print(f"  误差: ({calibration_data['error_x']}, {calibration_data['error_y']})")
        
        # 移动到下一个校准点
        self.current_point_index += 1
        
        if self.current_point_index < len(self.grid_points):
            self._move_to_calibration_point(self.current_point_index)
            return True
        else:
            # 所有校准点完成，计算校准参数
            return self._calculate_calibration_parameters()
    
    def _calculate_calibration_parameters(self):
        """计算校准参数"""
        if len(self.calibration_points) < 4:
            print("校准点数量不足，无法计算校准参数")
            return False
        
        try:
            # 计算平均偏移量
            total_error_x = sum(point['error_x'] for point in self.calibration_points)
            total_error_y = sum(point['error_y'] for point in self.calibration_points)
            
            self.offset_x = total_error_x / len(self.calibration_points)
            self.offset_y = total_error_y / len(self.calibration_points)
            
            # 计算缩放系数 (简化版本，使用线性回归会更准确)
            # 这里使用简单的平均方法
            x_ratios = []
            y_ratios = []
            
            for point in self.calibration_points:
                if point['target'][0] != 80:  # 避免除零
                    ratio_x = point['actual'][0] / point['target'][0]
                    x_ratios.append(ratio_x)
                
                if point['target'][1] != 60:  # 避免除零
                    ratio_y = point['actual'][1] / point['target'][1]
                    y_ratios.append(ratio_y)
            
            if x_ratios:
                self.scale_x = sum(x_ratios) / len(x_ratios)
            if y_ratios:
                self.scale_y = sum(y_ratios) / len(y_ratios)
            
            # 限制缩放系数范围
            self.scale_x = max(0.5, min(2.0, self.scale_x))
            self.scale_y = max(0.5, min(2.0, self.scale_y))
            
            self.is_calibrated = True
            self.calibration_mode = False
            
            print("校准参数计算完成:")
            print(f"  X轴偏移: {self.offset_x:.2f}")
            print(f"  Y轴偏移: {self.offset_y:.2f}")
            print(f"  X轴缩放: {self.scale_x:.3f}")
            print(f"  Y轴缩放: {self.scale_y:.3f}")
            
            # 保存校准参数
            self.save_calibration()
            
            return True
            
        except Exception as e:
            print(f"校准参数计算失败: {e}")
            return False
    
    def apply_calibration(self, target_x, target_y):
        """应用校准参数修正目标坐标"""
        if not self.is_calibrated:
            return target_x, target_y
        
        try:
            # 应用偏移和缩放
            corrected_x = (target_x - self.offset_x) * self.scale_x
            corrected_y = (target_y - self.offset_y) * self.scale_y
            
            # 确保坐标在有效范围内
            corrected_x = max(0, min(160, corrected_x))
            corrected_y = max(0, min(120, corrected_y))
            
            return corrected_x, corrected_y
            
        except Exception as e:
            print(f"校准应用失败: {e}")
            return target_x, target_y
    
    def save_calibration(self):
        """保存校准参数到文件"""
        try:
            calibration_data = {
                'offset_x': self.offset_x,
                'offset_y': self.offset_y,
                'scale_x': self.scale_x,
                'scale_y': self.scale_y,
                'is_calibrated': self.is_calibrated,
                'calibration_time': time.time(),
                'calibration_points': self.calibration_points
            }
            
            filename = self.calib_config['calibration_file']
            
            # 在K210上，可能需要使用简单的文件操作
            with open(filename, 'w') as f:
                # 简化的JSON写入 (K210可能不支持完整的json模块)
                f.write(str(calibration_data))
            
            print(f"校准参数已保存到 {filename}")
            return True
            
        except Exception as e:
            print(f"校准参数保存失败: {e}")
            return False
    
    def load_calibration(self):
        """从文件加载校准参数"""
        try:
            filename = self.calib_config['calibration_file']
            
            with open(filename, 'r') as f:
                data_str = f.read()
                # 简化的数据解析 (K210环境限制)
                calibration_data = eval(data_str)
            
            self.offset_x = calibration_data.get('offset_x', 0)
            self.offset_y = calibration_data.get('offset_y', 0)
            self.scale_x = calibration_data.get('scale_x', 1.0)
            self.scale_y = calibration_data.get('scale_y', 1.0)
            self.is_calibrated = calibration_data.get('is_calibrated', False)
            
            if self.is_calibrated:
                print("校准参数加载成功")
                print(f"  偏移: ({self.offset_x:.2f}, {self.offset_y:.2f})")
                print(f"  缩放: ({self.scale_x:.3f}, {self.scale_y:.3f})")
            else:
                print("未找到有效的校准参数")
            
            return True
            
        except Exception as e:
            print(f"校准参数加载失败: {e}")
            # 使用默认参数
            self.offset_x = 0
            self.offset_y = 0
            self.scale_x = 1.0
            self.scale_y = 1.0
            self.is_calibrated = False
            return False
    
    def reset_calibration(self):
        """重置校准参数"""
        self.offset_x = 0
        self.offset_y = 0
        self.scale_x = 1.0
        self.scale_y = 1.0
        self.is_calibrated = False
        self.calibration_mode = False
        self.calibration_points = []
        self.current_point_index = 0
        
        print("校准参数已重置")
    
    def get_calibration_status(self):
        """获取校准状态"""
        return {
            'is_calibrated': self.is_calibrated,
            'calibration_mode': self.calibration_mode,
            'offset_x': self.offset_x,
            'offset_y': self.offset_y,
            'scale_x': self.scale_x,
            'scale_y': self.scale_y,
            'current_point_index': self.current_point_index,
            'total_points': len(self.grid_points),
            'completed_points': len(self.calibration_points)
        }
    
    def draw_calibration_grid(self, img):
        """在图像上绘制校准网格"""
        if img is None:
            return
        
        try:
            # 绘制校准网格点
            for i, point in enumerate(self.grid_points):
                x, y = point
                
                if self.calibration_mode and i == self.current_point_index:
                    # 当前校准点用红色标记
                    color = (255, 0, 0)
                    size = 8
                elif i < len(self.calibration_points):
                    # 已完成的校准点用绿色标记
                    color = (0, 255, 0)
                    size = 6
                else:
                    # 未完成的校准点用蓝色标记
                    color = (0, 0, 255)
                    size = 4
                
                img.draw_circle(x, y, size, color=color, fill=False, thickness=2)
                img.draw_string(x + 10, y - 5, str(i + 1), color=color, scale=1)
            
            # 绘制校准状态信息
            if self.calibration_mode:
                status_text = f"Calibrating: {self.current_point_index + 1}/{len(self.grid_points)}"
                img.draw_string(0, 100, status_text, color=(255, 255, 0), scale=1)
            elif self.is_calibrated:
                img.draw_string(0, 100, "Calibrated", color=(0, 255, 0), scale=1)
            else:
                img.draw_string(0, 100, "Not Calibrated", color=(255, 0, 0), scale=1)
                
        except Exception as e:
            print(f"校准网格绘制失败: {e}")
