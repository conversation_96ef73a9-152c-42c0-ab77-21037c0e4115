# 野火K210激光瞄准系统配置管理模块
# 版权所有: 米醋电子工作室

class Config:
    """系统配置管理类 - 统一管理所有配置参数"""
    
    # 摄像头配置参数
    CAMERA_SETTINGS = {
        'pixformat': 'GRAYSCALE',  # 灰度模式
        'framesize': 'QQVGA',      # QQVGA分辨率 (160x120)
        'brightness': -2,          # 亮度调整
        'saturation': -2,          # 饱和度调整
        'skip_frames_time': 2000,  # 跳帧时间(ms)
        'dual_buff': False         # 不使用双缓冲
    }
    
    # LCD显示配置
    LCD_SETTINGS = {
        'clear_color': 'WHITE',    # 清屏颜色
        'fps_color': (255, 0, 0),  # FPS显示颜色(红色)
        'title_color': (0, 255, 0), # 标题颜色(绿色)
        'info_color': (0, 0, 255),  # 信息颜色(蓝色)
        'cross_color': (255, 0, 0), # 十字线颜色(红色)
        'cross_scale': 4           # 十字线大小
    }
    
    # 舵机控制参数
    SERVO_SETTINGS = {
        'ID1': 0x01,              # X轴舵机ID
        'ID2': 0x02,              # Y轴舵机ID
        'interval': 1000,         # 控制间隔(ms)
        'center_x': 2048,         # X轴中心位置
        'center_y': 2025,         # Y轴中心位置
        'min_position': 500,      # 最小位置限制
        'max_position': 3500,     # 最大位置限制
        'left_position': 1000,    # 左转位置
        'right_position': 3000    # 右转位置
    }
    
    # 串口通信配置
    UART_SETTINGS = {
        'uart_num': 1,            # 使用UART1
        'baudrate': 115200,       # 波特率
        'read_buf_len': 4096,     # 读缓冲区大小
        'rx_pin': 0,              # RX引脚(野火K210标准)
        'tx_pin': 1,              # TX引脚(野火K210标准)
        'timeout': 100            # 超时时间(ms)
    }
    
    # PID控制参数
    PID_PARAMS = {
        'x_axis': {
            'kp': 0.8,            # 比例系数
            'ki': 0.2,            # 积分系数
            'kd': 0.1,            # 微分系数
            'setpoint': 80,       # 目标位置(图像中心X)
            'output_limit': 500   # 输出限制
        },
        'y_axis': {
            'kp': 0.8,            # 比例系数
            'ki': 0.2,            # 积分系数
            'kd': 0.1,            # 微分系数
            'setpoint': 60,       # 目标位置(图像中心Y)
            'output_limit': 500   # 输出限制
        },
        'deadzone': 2,            # 死区范围(像素)
        'integral_limit': 1000,   # 积分限制
        'derivative_filter': 0.1  # 微分滤波系数
    }
    
    # 图像处理参数
    VISION_SETTINGS = {
        'black_threshold': (0, 50),      # 黑色阈值范围
        'min_area': 500,                 # 最小目标面积
        'max_area': 5000,                # 最大目标面积
        'rect_threshold': 30000,         # 矩形检测阈值
        'morphology_size': 1,            # 形态学处理核大小
        'aspect_ratio_min': 0.5,         # 最小宽高比
        'aspect_ratio_max': 2.0,         # 最大宽高比
        'center_filter_alpha': 0.3       # 中心点滤波系数
    }
    
    # 校准参数
    CALIBRATION_SETTINGS = {
        'offset_x': 0,            # X轴偏移量
        'offset_y': 0,            # Y轴偏移量
        'scale_x': 1.0,           # X轴缩放系数
        'scale_y': 1.0,           # Y轴缩放系数
        'calibration_points': 9,  # 校准点数量(3x3网格)
        'calibration_file': 'calibration.json'  # 校准文件名
    }
    
    # 系统运行参数
    SYSTEM_SETTINGS = {
        'gc_interval': 100,       # 垃圾回收间隔(帧)
        'debug_mode': True,       # 调试模式
        'log_level': 'INFO',      # 日志级别
        'max_fps': 30,            # 最大帧率限制
        'watchdog_timeout': 5000  # 看门狗超时(ms)
    }
    
    # 串口指令映射
    UART_COMMANDS = {
        '2': 'center',            # 回中位指令
        '3': 'left',              # 左转指令
        '4': 'center',            # 回中位指令
        '5': 'right'              # 右转指令
    }
    
    @classmethod
    def get_camera_settings(cls):
        """获取摄像头配置"""
        return cls.CAMERA_SETTINGS.copy()
    
    @classmethod
    def get_servo_settings(cls):
        """获取舵机配置"""
        return cls.SERVO_SETTINGS.copy()
    
    @classmethod
    def get_pid_params(cls):
        """获取PID参数"""
        return cls.PID_PARAMS.copy()
    
    @classmethod
    def get_uart_settings(cls):
        """获取串口配置"""
        return cls.UART_SETTINGS.copy()
    
    @classmethod
    def get_vision_settings(cls):
        """获取视觉处理配置"""
        return cls.VISION_SETTINGS.copy()
    
    @classmethod
    def get_calibration_settings(cls):
        """获取校准配置"""
        return cls.CALIBRATION_SETTINGS.copy()
    
    @classmethod
    def get_system_settings(cls):
        """获取系统配置"""
        return cls.SYSTEM_SETTINGS.copy()
    
    @classmethod
    def validate_config(cls):
        """验证配置参数的有效性"""
        errors = []
        
        # 验证舵机位置范围
        servo = cls.SERVO_SETTINGS
        if servo['center_x'] < servo['min_position'] or servo['center_x'] > servo['max_position']:
            errors.append("舵机X轴中心位置超出范围")
        
        if servo['center_y'] < servo['min_position'] or servo['center_y'] > servo['max_position']:
            errors.append("舵机Y轴中心位置超出范围")
        
        # 验证PID参数
        for axis in ['x_axis', 'y_axis']:
            pid = cls.PID_PARAMS[axis]
            if pid['kp'] < 0 or pid['ki'] < 0 or pid['kd'] < 0:
                errors.append(f"PID参数不能为负数: {axis}")
        
        # 验证图像处理参数
        vision = cls.VISION_SETTINGS
        if vision['min_area'] >= vision['max_area']:
            errors.append("最小面积不能大于等于最大面积")
        
        return errors
    
    @classmethod
    def print_config_summary(cls):
        """打印配置摘要"""
        print("=== 野火K210激光瞄准系统配置 ===")
        print(f"摄像头: {cls.CAMERA_SETTINGS['pixformat']} {cls.CAMERA_SETTINGS['framesize']}")
        print(f"舵机: ID1={cls.SERVO_SETTINGS['ID1']}, ID2={cls.SERVO_SETTINGS['ID2']}")
        print(f"串口: UART{cls.UART_SETTINGS['uart_num']} @ {cls.UART_SETTINGS['baudrate']}")
        print(f"PID: Kp={cls.PID_PARAMS['x_axis']['kp']}, Ki={cls.PID_PARAMS['x_axis']['ki']}, Kd={cls.PID_PARAMS['x_axis']['kd']}")
        print(f"调试模式: {'开启' if cls.SYSTEM_SETTINGS['debug_mode'] else '关闭'}")
        print("================================")
