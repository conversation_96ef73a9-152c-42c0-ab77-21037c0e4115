# -*- coding: utf-8 -*-
# 野火K210单线控制双舵机测试程序
# 版权所有: 米醋电子工作室

"""
单线控制双舵机测试程序

专门用于测试单线控制双舵机的硬件连接和功能。
适用于K210引脚0连接到上方舵机S信号线的硬件配置。
"""

def test_hardware_connection():
    """测试硬件连接"""
    print("=== 测试硬件连接 ===")
    
    try:
        from hardware_manager import HardwareManager
        
        # 创建硬件管理器
        hardware = HardwareManager()
        print("✓ 硬件管理器创建成功")
        
        # 初始化串口（单线模式）
        if hardware.init_uart():
            print("✓ 单线模式串口初始化成功")
        else:
            print("✗ 串口初始化失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 硬件连接测试失败: {e}")
        return False

def test_servo_individual():
    """测试单个舵机控制"""
    print("\n=== 测试单个舵机控制 ===")
    
    try:
        from hardware_manager import HardwareManager
        import time
        
        hardware = HardwareManager()
        hardware.init_uart()
        
        # 测试X轴舵机（ID1，上方舵机）
        print("测试X轴舵机（ID1）...")
        success1 = hardware.send_servo_command(0x01, 2048, 1000)  # 中心位置
        time.sleep(2)
        
        success2 = hardware.send_servo_command(0x01, 1500, 1000)  # 左转
        time.sleep(2)
        
        success3 = hardware.send_servo_command(0x01, 2500, 1000)  # 右转
        time.sleep(2)
        
        success4 = hardware.send_servo_command(0x01, 2048, 1000)  # 回中心
        time.sleep(1)
        
        if success1 and success2 and success3 and success4:
            print("✓ X轴舵机测试通过")
        else:
            print("✗ X轴舵机测试失败")
            return False
        
        # 测试Y轴舵机（ID2，下方舵机）
        print("测试Y轴舵机（ID2）...")
        success1 = hardware.send_servo_command(0x02, 2025, 1000)  # 中心位置
        time.sleep(2)
        
        success2 = hardware.send_servo_command(0x02, 1500, 1000)  # 下转
        time.sleep(2)
        
        success3 = hardware.send_servo_command(0x02, 2500, 1000)  # 上转
        time.sleep(2)
        
        success4 = hardware.send_servo_command(0x02, 2025, 1000)  # 回中心
        time.sleep(1)
        
        if success1 and success2 and success3 and success4:
            print("✓ Y轴舵机测试通过")
        else:
            print("✗ Y轴舵机测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 单个舵机测试失败: {e}")
        return False

def test_dual_servo_control():
    """测试双舵机协调控制"""
    print("\n=== 测试双舵机协调控制 ===")
    
    try:
        from hardware_manager import HardwareManager
        import time
        
        hardware = HardwareManager()
        hardware.init_uart()
        
        # 测试双舵机协调动作
        test_positions = [
            (2048, 2025, "中心位置"),
            (1500, 1500, "左下角"),
            (2500, 1500, "右下角"),
            (2500, 2500, "右上角"),
            (1500, 2500, "左上角"),
            (2048, 2025, "回到中心")
        ]
        
        for x_pos, y_pos, description in test_positions:
            print(f"移动到{description}...")
            success = hardware.send_dual_servo_command(x_pos, y_pos, 1000)
            
            if success:
                print(f"✓ {description} - 成功")
            else:
                print(f"✗ {description} - 失败")
                return False
            
            time.sleep(2)  # 等待舵机到位
        
        print("✓ 双舵机协调控制测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 双舵机协调控制测试失败: {e}")
        return False

def test_preset_positions():
    """测试预设位置功能"""
    print("\n=== 测试预设位置功能 ===")
    
    try:
        from hardware_manager import HardwareManager
        import time
        
        hardware = HardwareManager()
        hardware.init_uart()
        
        # 测试预设位置
        positions = [
            ("中心位置", hardware.set_servo_center_position),
            ("左转位置", hardware.set_servo_left_position),
            ("右转位置", hardware.set_servo_right_position),
            ("回到中心", hardware.set_servo_center_position)
        ]
        
        for pos_name, pos_func in positions:
            print(f"测试{pos_name}...")
            success = pos_func()
            
            if success:
                print(f"✓ {pos_name} - 成功")
            else:
                print(f"✗ {pos_name} - 失败")
                return False
            
            time.sleep(2)  # 等待舵机到位
        
        print("✓ 预设位置功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 预设位置功能测试失败: {e}")
        return False

def test_timing_analysis():
    """测试指令发送时序"""
    print("\n=== 测试指令发送时序 ===")
    
    try:
        from hardware_manager import HardwareManager
        import time
        
        hardware = HardwareManager()
        hardware.init_uart()
        
        # 测试快速连续指令
        print("测试快速连续指令发送...")
        start_time = time.time()
        
        for i in range(5):
            success = hardware.send_dual_servo_command(
                2048 + i * 100, 
                2025 + i * 50, 
                500
            )
            if not success:
                print(f"✗ 第{i+1}次指令发送失败")
                return False
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"✓ 5次指令发送完成，总耗时: {total_time:.2f}秒")
        print(f"✓ 平均每次指令耗时: {total_time/5:.3f}秒")
        
        if total_time < 2.0:  # 期望在2秒内完成
            print("✓ 指令发送时序测试通过")
            return True
        else:
            print("⚠️ 指令发送较慢，可能需要优化")
            return True
        
    except Exception as e:
        print(f"✗ 指令发送时序测试失败: {e}")
        return False

def run_single_wire_test():
    """运行完整的单线控制测试"""
    print("野火K210单线控制双舵机测试")
    print("版权所有: 米醋电子工作室")
    print("=" * 50)
    
    tests = [
        ("硬件连接", test_hardware_connection),
        ("单个舵机控制", test_servo_individual),
        ("双舵机协调控制", test_dual_servo_control),
        ("预设位置功能", test_preset_positions),
        ("指令发送时序", test_timing_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name} - 测试通过")
            else:
                print(f"\n❌ {test_name} - 测试失败")
        except Exception as e:
            print(f"\n💥 {test_name} - 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！单线控制双舵机系统工作正常。")
        print("\n📋 硬件连接确认:")
        print("✓ K210引脚0 → 上方舵机S信号线")
        print("✓ K210 GND → 舵机电源地线")
        print("✓ K210 5V → 舵机电源正极")
        print("✓ 上下舵机间三根连接线正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查硬件连接：")
        print("\n🔧 检查项目:")
        print("- 确认K210引脚0连接到上方舵机S线")
        print("- 检查舵机电源供应是否充足")
        print("- 验证舵机间连接线是否牢固")
        print("- 确认舵机ID设置正确（ID1=0x01, ID2=0x02）")
        return False

def main():
    """主函数"""
    try:
        success = run_single_wire_test()
        
        if success:
            print("\n🚀 准备启动完整激光瞄准系统...")
            print("运行命令: python main.py")
        else:
            print("\n🔧 请先解决硬件连接问题")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n程序异常: {e}")

if __name__ == "__main__":
    main()
